2025-06-14 19:22:23 - test_module - DEBUG - test_logging_system:81 - Debug message
2025-06-14 19:22:23 - test_module - [32mINFO[0m - test_logging_system:82 - Info message
2025-06-14 19:22:23 - test_module - [33mWARNING[0m - test_logging_system:83 - Warning message
2025-06-14 19:22:23 - test_module - [31mERROR[0m - test_logging_system:84 - Error message
2025-06-14 19:22:23 - test_module - [32mINFO[0m - __exit__:146 - Test operation completed in 0.100s
2025-06-14 19:22:23 - audit - [32mIN<PERSON>O[0m - log_user_action:111 - User 1 - test_action - test details
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM users WHERE username = ?...
2025-06-14 19:22:23 - database - DEBUG - wrapper:188 - Query parameters: ('admin',)
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM users WHERE username = ?... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM users WHERE username = ?... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM vehicle_types LIMIT 1...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM vehicle_types LIMIT 1... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM vehicle_types LIMIT 1... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM depositor_types LIMIT 1...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM depositor_types LIMIT 1... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM depositor_types LIMIT 1... completed in 0.002s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM sectors LIMIT 1...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM sectors LIMIT 1... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM sectors LIMIT 1... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM zones LIMIT 1...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM zones LIMIT 1... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM zones LIMIT 1... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM occupation_types LIMIT 1...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM occupation_types LIMIT 1... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM occupation_types LIMIT 1... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM vehicle_types... completed in 0.001s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types...
2025-06-14 19:22:23 - database - DEBUG - fetch_all:99 - Cache hit for query: SELECT * FROM vehicle_types...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types... completed in 0.000s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) FROM (SELECT * FROM vehicle_types)...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) FROM (SELECT * FROM vehicle_types)... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) FROM (SELECT * FROM vehicle_types)... completed in 0.001s
2025-06-14 19:22:23 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types LIMIT 2 OFFSET 0...
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM vehicle_types LIMIT 2 OFFSET 0... completed in 0.000s
2025-06-14 19:22:23 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types LIMIT 2 OFFSET 0... completed in 0.001s
2025-06-14 19:22:23 - database - [32mINFO[0m - bulk_insert:1001 - Inserted batch 1: 2 records
2025-06-14 19:22:23 - database - [32mINFO[0m - invalidate_cache:931 - Invalidated 0 cache entries matching 'vehicle_types'
2025-06-14 19:22:23 - database - [32mINFO[0m - bulk_insert:1008 - Successfully inserted 2 records into vehicle_types
2025-06-14 19:22:23 - database - [32mINFO[0m - invalidate_cache:931 - Invalidated 0 cache entries matching 'vehicle_types'
2025-06-14 19:23:04 - test_module - DEBUG - test_logging_system:81 - Debug message
2025-06-14 19:23:04 - test_module - [32mINFO[0m - test_logging_system:82 - Info message
2025-06-14 19:23:04 - test_module - [33mWARNING[0m - test_logging_system:83 - Warning message
2025-06-14 19:23:04 - test_module - [31mERROR[0m - test_logging_system:84 - Error message
2025-06-14 19:23:04 - test_module - [32mINFO[0m - __exit__:146 - Test operation completed in 0.101s
2025-06-14 19:23:04 - audit - [32mINFO[0m - log_user_action:111 - User 1 - test_action - test details
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM users WHERE username = ?...
2025-06-14 19:23:04 - database - DEBUG - wrapper:188 - Query parameters: ('admin',)
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM users WHERE username = ?... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM users WHERE username = ?... completed in 0.000s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM vehicle_types LIMIT 1...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM vehicle_types LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM vehicle_types LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM depositor_types LIMIT 1...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM depositor_types LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM depositor_types LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM sectors LIMIT 1...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM sectors LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM sectors LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM zones LIMIT 1...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM zones LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM zones LIMIT 1... completed in 0.001s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM occupation_types LIMIT 1...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM occupation_types LIMIT 1... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM occupation_types LIMIT 1... completed in 0.001s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM vehicle_types... completed in 0.001s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types... completed in 0.001s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types...
2025-06-14 19:23:04 - database - DEBUG - fetch_all:99 - Cache hit for query: SELECT * FROM vehicle_types...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types... completed in 0.000s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) FROM (SELECT * FROM vehicle_types)...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) FROM (SELECT * FROM vehicle_types)... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) FROM (SELECT * FROM vehicle_types)... completed in 0.000s
2025-06-14 19:23:04 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types LIMIT 2 OFFSET 0...
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM vehicle_types LIMIT 2 OFFSET 0... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types LIMIT 2 OFFSET 0... completed in 0.000s
2025-06-14 19:23:04 - database - [32mINFO[0m - bulk_insert:1001 - Inserted batch 1: 2 records
2025-06-14 19:23:04 - database - [32mINFO[0m - invalidate_cache:931 - Invalidated 0 cache entries matching 'vehicle_types'
2025-06-14 19:23:04 - database - [32mINFO[0m - bulk_insert:1008 - Successfully inserted 2 records into vehicle_types
2025-06-14 19:23:04 - database - [32mINFO[0m - invalidate_cache:931 - Invalidated 0 cache entries matching 'vehicle_types'
2025-06-14 19:34:08 - utils.cache - [32mINFO[0m - create_cache:186 - Created cache 'database' with TTL=300.0s, max_size=500
2025-06-14 19:34:08 - utils.cache - [32mINFO[0m - create_cache:186 - Created cache 'ui' with TTL=60.0s, max_size=100
2025-06-14 19:34:08 - utils.cache - [32mINFO[0m - create_cache:186 - Created cache 'config' with TTL=3600.0s, max_size=50
