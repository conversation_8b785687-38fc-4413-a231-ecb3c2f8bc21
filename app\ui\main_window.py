#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main window for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget,
    QPushButton, QLabel, QFrame, QSizePolicy, QSpacerItem, QMessageBox
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont

from ui.login_dialog import LoginDialog
from ui.dashboard import DashboardWidget
from ui.admin_panel import AdminPanelWidget
from ui.modules.impound import ImpoundWidget
from ui.modules.shops import ShopsWidget
from ui.modules.souks import SouksWidget
from ui.modules.beverages import BeveragesWidget
from ui.modules.parking import ParkingWidget
from ui.modules.occupation import OccupationWidget
from ui.modules.lands import LandsWidget

from utils.auth import Auth

class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self, db_manager, config):
        """Initialize main window"""
        super().__init__()
        
        self.db_manager = db_manager
        self.config = config
        self.auth = Auth(db_manager)
        
        self.setWindowTitle("Gestion des Ressources Fiscales Communales")
        self.resize(
            self.config.get_setting("window_size.width", 1200),
            self.config.get_setting("window_size.height", 800)
        )
        
        self.setup_ui()
        self.show_login()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create sidebar
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setMinimumWidth(250)
        self.sidebar.setMaximumWidth(250)
        self.sidebar.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
        
        # Create sidebar layout
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # Create app title
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_frame.setMinimumHeight(60)
        title_frame.setMaximumHeight(60)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 0, 0, 0)
        
        title_label = QLabel("GestionFiscale")
        title_label.setObjectName("titleLabel")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        title_layout.addWidget(title_label)
        sidebar_layout.addWidget(title_frame)
        
        # Create menu buttons
        self.menu_frame = QFrame()
        self.menu_frame.setObjectName("menuFrame")
        
        menu_layout = QVBoxLayout(self.menu_frame)
        menu_layout.setContentsMargins(0, 10, 0, 10)
        menu_layout.setSpacing(5)
        
        # Dashboard button
        self.dashboard_btn = self.create_menu_button("Tableau de Bord", "dashboard")
        menu_layout.addWidget(self.dashboard_btn)
        
        # Impound management button
        self.impound_btn = self.create_menu_button("Droits de Fourrière", "impound")
        menu_layout.addWidget(self.impound_btn)
        
        # Commercial shops button
        self.shops_btn = self.create_menu_button("Locaux Commerciaux", "shops")
        menu_layout.addWidget(self.shops_btn)
        
        # Souk leasing button
        self.souks_btn = self.create_menu_button("Affermage des Souks", "souks")
        menu_layout.addWidget(self.souks_btn)
        
        # Beverage establishments button
        self.beverages_btn = self.create_menu_button("Débits de Boissons", "beverages")
        menu_layout.addWidget(self.beverages_btn)
        
        # Parking rights button
        self.parking_btn = self.create_menu_button("Droit de Stationnement", "parking")
        menu_layout.addWidget(self.parking_btn)
        
        # Temporary occupation button
        self.occupation_btn = self.create_menu_button("Occupation Temporaire", "occupation")
        menu_layout.addWidget(self.occupation_btn)
        
        # Undeveloped lands button
        self.lands_btn = self.create_menu_button("Terrains Non Bâtis", "lands")
        menu_layout.addWidget(self.lands_btn)
        
        # Admin panel button
        self.admin_btn = self.create_menu_button("Administration", "admin")
        menu_layout.addWidget(self.admin_btn)
        
        # Add spacer
        menu_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Language switcher
        language_frame = QFrame()
        language_layout = QHBoxLayout(language_frame)
        language_layout.setContentsMargins(20, 10, 20, 10)
        
        self.fr_btn = QPushButton("FR")
        self.fr_btn.setObjectName("languageButton")
        self.fr_btn.setCheckable(True)
        self.fr_btn.setChecked(self.config.get_setting("language") == "fr")
        self.fr_btn.clicked.connect(lambda: self.switch_language("fr"))
        
        self.ar_btn = QPushButton("AR")
        self.ar_btn.setObjectName("languageButton")
        self.ar_btn.setCheckable(True)
        self.ar_btn.setChecked(self.config.get_setting("language") == "ar")
        self.ar_btn.clicked.connect(lambda: self.switch_language("ar"))
        
        language_layout.addWidget(self.fr_btn)
        language_layout.addWidget(self.ar_btn)
        
        menu_layout.addWidget(language_frame)
        
        # User info and logout
        self.user_frame = QFrame()
        self.user_frame.setObjectName("userFrame")
        self.user_frame.setMinimumHeight(80)
        self.user_frame.setMaximumHeight(80)
        
        user_layout = QVBoxLayout(self.user_frame)
        user_layout.setContentsMargins(20, 10, 20, 20)
        
        self.user_label = QLabel("Non connecté")
        self.user_label.setObjectName("userLabel")
        
        self.logout_btn = QPushButton("Déconnexion")
        self.logout_btn.setObjectName("logoutButton")
        self.logout_btn.clicked.connect(self.logout)
        
        user_layout.addWidget(self.user_label)
        user_layout.addWidget(self.logout_btn)
        
        menu_layout.addWidget(self.user_frame)
        
        sidebar_layout.addWidget(self.menu_frame)
        
        # Add sidebar to main layout
        main_layout.addWidget(self.sidebar)
        
        # Create content area
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("contentArea")
        
        # Create and add widgets to content area
        self.dashboard_widget = DashboardWidget(self.db_manager, self.auth)
        self.admin_panel_widget = AdminPanelWidget(self.db_manager, self.auth)
        self.impound_widget = ImpoundWidget(self.db_manager, self.auth)
        self.shops_widget = ShopsWidget(self.db_manager, self.auth)
        self.souks_widget = SouksWidget(self.db_manager, self.auth)
        self.beverages_widget = BeveragesWidget(self.db_manager, self.auth)
        self.parking_widget = ParkingWidget(self.db_manager, self.auth)
        self.occupation_widget = OccupationWidget(self.db_manager, self.auth)
        self.lands_widget = LandsWidget(self.db_manager, self.auth)
        
        self.content_area.addWidget(self.dashboard_widget)
        self.content_area.addWidget(self.admin_panel_widget)
        self.content_area.addWidget(self.impound_widget)
        self.content_area.addWidget(self.shops_widget)
        self.content_area.addWidget(self.souks_widget)
        self.content_area.addWidget(self.beverages_widget)
        self.content_area.addWidget(self.parking_widget)
        self.content_area.addWidget(self.occupation_widget)
        self.content_area.addWidget(self.lands_widget)
        
        # Add content area to main layout
        main_layout.addWidget(self.content_area)
        
        # Connect menu buttons
        self.dashboard_btn.clicked.connect(lambda: self.switch_page(0))
        self.admin_btn.clicked.connect(lambda: self.switch_page(1))
        self.impound_btn.clicked.connect(lambda: self.switch_page(2))
        self.shops_btn.clicked.connect(lambda: self.switch_page(3))
        self.souks_btn.clicked.connect(lambda: self.switch_page(4))
        self.beverages_btn.clicked.connect(lambda: self.switch_page(5))
        self.parking_btn.clicked.connect(lambda: self.switch_page(6))
        self.occupation_btn.clicked.connect(lambda: self.switch_page(7))
        self.lands_btn.clicked.connect(lambda: self.switch_page(8))
        
        # Set initial page
        self.content_area.setCurrentIndex(0)
        
        # Apply stylesheet
        self.apply_stylesheet()
    
    def create_menu_button(self, text, object_name):
        """Create a menu button for the sidebar"""
        button = QPushButton(text)
        button.setObjectName(f"{object_name}Button")
        button.setMinimumHeight(50)
        button.setIconSize(QSize(24, 24))
        button.setCheckable(True)
        
        return button
    
    def switch_page(self, index):
        """Switch to a different page in the content area"""
        if not self.auth.is_authenticated():
            self.show_login()
            return
        
        # Uncheck all buttons
        for button in self.menu_frame.findChildren(QPushButton):
            if button.objectName().endswith("Button"):
                button.setChecked(False)
        
        # Check the active button
        buttons = [
            self.dashboard_btn, self.admin_btn, self.impound_btn,
            self.shops_btn, self.souks_btn, self.beverages_btn,
            self.parking_btn, self.occupation_btn, self.lands_btn
        ]
        
        if 0 <= index < len(buttons):
            buttons[index].setChecked(True)
        
        # Switch page
        self.content_area.setCurrentIndex(index)
    
    def switch_language(self, language):
        """Switch application language"""
        if language == "fr":
            self.fr_btn.setChecked(True)
            self.ar_btn.setChecked(False)
        else:
            self.fr_btn.setChecked(False)
            self.ar_btn.setChecked(True)
        
        self.config.set_setting("language", language)
        
        # Show message to restart application
        QMessageBox.information(
            self,
            "Changement de Langue",
            "Veuillez redémarrer l'application pour appliquer le changement de langue."
        )
    
    def show_login(self):
        """Show login dialog"""
        login_dialog = LoginDialog(self.auth)
        result = login_dialog.exec_()
        
        if result == LoginDialog.Accepted:
            self.update_user_info()
            self.switch_page(0)  # Show dashboard
        else:
            # Exit application if login canceled
            self.close()
    
    def logout(self):
        """Log out the current user"""
        self.auth.logout()
        self.show_login()
    
    def update_user_info(self):
        """Update user information in the UI"""
        user = self.auth.get_current_user()
        if user:
            self.user_label.setText(f"{user['full_name']} ({user['role']})")
            
            # Show/hide admin button based on permissions
            self.admin_btn.setVisible(self.auth.has_permission("manage_users"))
    
    def apply_stylesheet(self):
        """Apply stylesheet to the application"""
        # Base styles
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            #sidebar {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: none;
            }
            
            #titleFrame {
                background-color: #1a2530;
                border: none;
            }
            
            #titleLabel {
                color: #ecf0f1;
            }
            
            #menuFrame QPushButton {
                background-color: transparent;
                color: #ecf0f1;
                border: none;
                text-align: left;
                padding-left: 20px;
                font-size: 14px;
            }
            
            #menuFrame QPushButton:hover {
                background-color: #34495e;
            }
            
            #menuFrame QPushButton:checked {
                background-color: #3498db;
                border-left: 4px solid #ecf0f1;
            }
            
            #userFrame {
                background-color: #1a2530;
                border: none;
            }
            
            #userLabel {
                color: #ecf0f1;
                font-size: 12px;
            }
            
            #logoutButton {
                background-color: #e74c3c;
                color: #ecf0f1;
                border: none;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            
            #logoutButton:hover {
                background-color: #c0392b;
            }
            
            #languageButton {
                background-color: #7f8c8d;
                color: #ecf0f1;
                border: none;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            
            #languageButton:checked {
                background-color: #3498db;
            }
            
            #contentArea {
                background-color: #ecf0f1;
            }
        """)
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Save window size
        self.config.set_setting("window_size.width", self.width())
        self.config.set_setting("window_size.height", self.height())
        
        # Close database connection
        self.db_manager.disconnect()
        
        event.accept()