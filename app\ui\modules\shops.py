#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Commercial shops management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ShopsWidget(QWidget):
    """Commercial shops management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize shops widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Gestion des Locaux Commerciaux")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add shop button
        add_shop_btn = QPushButton("Ajouter une Boutique")
        add_shop_btn.clicked.connect(self.add_shop)
        header_layout.addWidget(add_shop_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create shops tab
        shops_tab = QWidget()
        shops_layout = QVBoxLayout(shops_tab)
        
        # Create shops table
        self.shops_table = QTableWidget()
        self.shops_table.setColumnCount(12)
        self.shops_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Surface", "État", "Propriétaire", 
            "CIN/RC", "Adresse", "Téléphone", "Date d'occupation", 
            "Mois non payés", "Tarif", "Total"
        ])
        self.shops_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.shops_table.verticalHeader().setVisible(False)
        
        shops_layout.addWidget(self.shops_table)
        
        # Create payment tab
        payment_tab = QWidget()
        payment_layout = QVBoxLayout(payment_tab)
        
        # TODO: Add payment interface
        payment_layout.addWidget(QLabel("Interface de paiement à implémenter"))
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Propriétaire", "CIN/RC", 
            "Période", "Montant", "N° Créance", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(9)
        self.history_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Propriétaire", "CIN/RC", 
            "Période", "Mois", "Total", "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Create unused shops tab
        unused_tab = QWidget()
        unused_layout = QVBoxLayout(unused_tab)
        
        # Create unused shops table
        self.unused_table = QTableWidget()
        self.unused_table.setColumnCount(5)
        self.unused_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Surface", "État", "Actions"
        ])
        self.unused_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.unused_table.verticalHeader().setVisible(False)
        
        unused_layout.addWidget(self.unused_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(shops_tab, "Liste des Boutiques")
        self.tab_widget.addTab(payment_tab, "Initiation Paiement")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        self.tab_widget.addTab(unused_tab, "Boutiques Non Exploitées")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_shop(self):
        """Add a new shop"""
        try:
            # Créer directement la boîte de dialogue sans import
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QLabel, QLineEdit, QPushButton, QComboBox, QDoubleSpinBox, QCheckBox, QTextEdit, QDateEdit, QDialogButtonBox, QGroupBox
            from PyQt5.QtCore import Qt, QDate
            
            # Créer une boîte de dialogue simple
            dialog = QDialog(self)
            dialog.setWindowTitle("Ajouter une Boutique")
            dialog.setMinimumWidth(500)
            dialog.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
            dialog.setStyleSheet("QDialog { background-color: #f5f5f5; }")
            
            # Créer le layout principal
            main_layout = QVBoxLayout(dialog)
            
            # Créer le groupe d'informations de la boutique
            shop_group = QGroupBox("Informations de la Boutique")
            shop_layout = QFormLayout(shop_group)
            
            # Secteur
            sector_combo = QComboBox()
            sector_combo.addItems(["Secteur 1", "Secteur 2", "Secteur 3", "Secteur 4"])
            shop_layout.addRow("Secteur:", sector_combo)
            
            # Numéro de boutique
            number_edit = QLineEdit()
            shop_layout.addRow("N° Boutique:", number_edit)
            
            # Surface
            surface_spin = QDoubleSpinBox()
            surface_spin.setRange(1, 1000)
            surface_spin.setSuffix(" m²")
            shop_layout.addRow("Surface:", surface_spin)
            
            # État
            state_combo = QComboBox()
            state_combo.addItems(["Bon état", "Moyen", "À rénover"])
            shop_layout.addRow("État:", state_combo)
            
            # Tarif
            rate_spin = QDoubleSpinBox()
            rate_spin.setRange(1, 10000)
            rate_spin.setSuffix(" DH/mois")
            shop_layout.addRow("Tarif Mensuel:", rate_spin)
            
            main_layout.addWidget(shop_group)
            
            # Créer le groupe d'informations du locataire
            tenant_group = QGroupBox("Informations du Locataire")
            tenant_layout = QFormLayout(tenant_group)
            
            # Boutique louée
            is_rented_check = QCheckBox("Boutique Louée")
            tenant_layout.addRow("", is_rented_check)
            
            # Nom du locataire
            tenant_name_edit = QLineEdit()
            tenant_layout.addRow("Nom Complet:", tenant_name_edit)
            
            # CIN/RC
            tenant_id_edit = QLineEdit()
            tenant_layout.addRow("CIN/RC:", tenant_id_edit)
            
            # Téléphone
            tenant_phone_edit = QLineEdit()
            tenant_layout.addRow("Téléphone:", tenant_phone_edit)
            
            # Date d'occupation
            occupation_date_edit = QDateEdit()
            occupation_date_edit.setCalendarPopup(True)
            occupation_date_edit.setDate(QDate.currentDate())
            tenant_layout.addRow("Date d'Occupation:", occupation_date_edit)
            
            main_layout.addWidget(tenant_group)
            
            # Fonction pour activer/désactiver les champs du locataire
            def toggle_tenant_fields(state):
                enabled = bool(state)
                tenant_name_edit.setEnabled(enabled)
                tenant_id_edit.setEnabled(enabled)
                tenant_phone_edit.setEnabled(enabled)
                occupation_date_edit.setEnabled(enabled)
            
            # Connecter la case à cocher
            is_rented_check.stateChanged.connect(toggle_tenant_fields)
            
            # Désactiver les champs par défaut
            toggle_tenant_fields(False)
            
            # Créer les boutons
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            
            main_layout.addWidget(button_box)
            
            # Afficher la boîte de dialogue
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Récupérer les données
                data = {
                    "sector": sector_combo.currentText(),
                    "number": number_edit.text(),
                    "surface": surface_spin.value(),
                    "state": state_combo.currentText(),
                    "rate": rate_spin.value(),
                    "is_rented": is_rented_check.isChecked()
                }
                
                if data["is_rented"]:
                    data.update({
                        "tenant_name": tenant_name_edit.text(),
                        "tenant_id": tenant_id_edit.text(),
                        "tenant_phone": tenant_phone_edit.text(),
                        "occupation_date": occupation_date_edit.date().toString("yyyy-MM-dd")
                    })
                
                # Vérifier les données
                if not data["number"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir tous les champs obligatoires."
                    )
                    return
                
                if data["is_rented"] and not data["tenant_name"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir les informations du locataire."
                    )
                    return
                
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Boutique Ajoutée",
                    f"La boutique {data['number']} du {data['sector']} a été ajoutée avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )