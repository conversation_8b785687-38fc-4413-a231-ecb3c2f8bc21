#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Application de Gestion des Ressources Fiscales Communales
Main application entry point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTranslator, QLocale
from PyQt5.QtGui import QFontDatabase, QFont
from ui.main_window import MainWindow
from utils.config import Config
from database.db_manager import DatabaseManager

def main():
    """Main application entry point"""
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("GestionFiscale")
    
    # Apply modern style
    style_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                             "ui", "resources", "style.qss")
    if os.path.exists(style_file):
        with open(style_file, "r") as f:
            app.setStyleSheet(f.read())
    
    # Load configuration
    config = Config()
    
    # Initialize database
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    # Setup translation
    translator = QTranslator()
    locale = config.get_setting("language", "fr")  # Default to French
    translator_file = f"resources/translations/{locale}.qm"
    if os.path.exists(translator_file):
        translator.load(translator_file)
        app.installTranslator(translator)
    
    # Create and show main window
    main_window = MainWindow(db_manager, config)
    main_window.show()
    
    # Execute application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()