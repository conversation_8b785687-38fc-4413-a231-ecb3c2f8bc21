#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Logging configuration for the Fiscal Resources Management Application
Provides structured logging with different levels and outputs
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional
import traceback


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        """Format log record with colors"""
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class ApplicationLogger:
    """Application-wide logging configuration"""
    
    def __init__(self, app_name: str = "FiscalApp", log_dir: str = "logs"):
        """Initialize application logger"""
        self.app_name = app_name
        self.log_dir = log_dir
        self.loggers = {}
        
        # Create log directory
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Setup root logger
        self.setup_root_logger()
    
    def setup_root_logger(self):
        """Setup root logger configuration"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with colors
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler for all logs
        log_file = os.path.join(self.log_dir, f"{self.app_name}.log")
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Error file handler
        error_file = os.path.join(self.log_dir, f"{self.app_name}_errors.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_file, maxBytes=5*1024*1024, backupCount=3, encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger for a specific module"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def log_exception(self, logger: logging.Logger, message: str = "An error occurred"):
        """Log exception with full traceback"""
        logger.error(f"{message}\n{traceback.format_exc()}")
    
    def log_user_action(self, user_id: int, action: str, details: str = ""):
        """Log user actions for audit trail"""
        audit_logger = self.get_logger("audit")
        audit_logger.info(f"User {user_id} - {action} - {details}")
    
    def log_database_operation(self, operation: str, table: str, record_id: Optional[int] = None, details: str = ""):
        """Log database operations"""
        db_logger = self.get_logger("database")
        record_info = f"ID:{record_id}" if record_id else "N/A"
        db_logger.info(f"{operation} - {table} - {record_info} - {details}")
    
    def log_performance(self, operation: str, duration: float, details: str = ""):
        """Log performance metrics"""
        perf_logger = self.get_logger("performance")
        perf_logger.info(f"{operation} - {duration:.3f}s - {details}")


class PerformanceTimer:
    """Context manager for timing operations"""
    
    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None):
        """Initialize performance timer"""
        self.operation_name = operation_name
        self.logger = logger or logging.getLogger("performance")
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        """Start timing"""
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End timing and log result"""
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.info(f"{self.operation_name} completed in {duration:.3f}s")
        else:
            self.logger.error(f"{self.operation_name} failed after {duration:.3f}s: {exc_val}")
    
    def get_duration(self) -> Optional[float]:
        """Get duration in seconds"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


def log_function_call(logger: Optional[logging.Logger] = None):
    """Decorator to log function calls"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_logger = logger or logging.getLogger(func.__module__)
            func_logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
            
            try:
                result = func(*args, **kwargs)
                func_logger.debug(f"{func.__name__} completed successfully")
                return result
            except Exception as e:
                func_logger.error(f"{func.__name__} failed: {e}")
                raise
        
        return wrapper
    return decorator


def log_database_query(logger: Optional[logging.Logger] = None):
    """Decorator to log database queries"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            db_logger = logger or logging.getLogger("database")
            
            # Extract query from args if possible
            query = args[1] if len(args) > 1 else "Unknown query"
            params = args[2] if len(args) > 2 else None
            
            db_logger.debug(f"Executing query: {query[:100]}...")
            if params:
                db_logger.debug(f"Query parameters: {params}")
            
            with PerformanceTimer(f"Query: {query[:50]}...", db_logger):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


class ErrorHandler:
    """Centralized error handling"""
    
    def __init__(self, logger: logging.Logger):
        """Initialize error handler"""
        self.logger = logger
    
    def handle_database_error(self, error: Exception, operation: str, context: str = ""):
        """Handle database errors"""
        error_msg = f"Database error during {operation}: {error}"
        if context:
            error_msg += f" (Context: {context})"
        
        self.logger.error(error_msg)
        return f"Erreur de base de données: {operation} a échoué"
    
    def handle_validation_error(self, error: Exception, field: str = "", value: str = ""):
        """Handle validation errors"""
        error_msg = f"Validation error for field '{field}' with value '{value}': {error}"
        self.logger.warning(error_msg)
        return f"Erreur de validation: {field} n'est pas valide"
    
    def handle_permission_error(self, user_id: int, action: str):
        """Handle permission errors"""
        error_msg = f"Permission denied for user {user_id} trying to {action}"
        self.logger.warning(error_msg)
        return "Vous n'avez pas les permissions nécessaires pour cette action"
    
    def handle_unexpected_error(self, error: Exception, context: str = ""):
        """Handle unexpected errors"""
        error_msg = f"Unexpected error: {error}"
        if context:
            error_msg += f" (Context: {context})"
        
        self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return "Une erreur inattendue s'est produite. Veuillez contacter l'administrateur."


# Global logger instance
app_logger = ApplicationLogger()

# Convenience functions
def get_logger(name: str) -> logging.Logger:
    """Get logger for module"""
    return app_logger.get_logger(name)

def log_user_action(user_id: int, action: str, details: str = ""):
    """Log user action"""
    app_logger.log_user_action(user_id, action, details)

def log_database_operation(operation: str, table: str, record_id: Optional[int] = None, details: str = ""):
    """Log database operation"""
    app_logger.log_database_operation(operation, table, record_id, details)

def log_performance(operation: str, duration: float, details: str = ""):
    """Log performance metric"""
    app_logger.log_performance(operation, duration, details)
