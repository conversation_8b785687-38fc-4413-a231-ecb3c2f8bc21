#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Souk leasing management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SouksWidget(QWidget):
    """Souk leasing management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize souks widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Produit d'Affermage des Souks Communaux")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add lease button
        add_lease_btn = QPushButton("Ajouter une Location")
        add_lease_btn.clicked.connect(self.add_lease)
        header_layout.addWidget(add_lease_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create leases tab
        leases_tab = QWidget()
        leases_layout = QVBoxLayout(leases_tab)
        
        # Create leases table
        self.leases_table = QTableWidget()
        self.leases_table.setColumnCount(8)
        self.leases_table.setHorizontalHeaderLabels([
            "Locataire", "CIN/RC", "Adresse", "Téléphone", 
            "Montant", "Dernier mois non payé", "Total", "Actions"
        ])
        self.leases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.leases_table.verticalHeader().setVisible(False)
        
        leases_layout.addWidget(self.leases_table)
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(7)
        self.pending_table.setHorizontalHeaderLabels([
            "Locataire", "CIN/RC", "Période", "Montant", 
            "N° Créance", "Date", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "Locataire", "CIN/RC", "Période", "Total", 
            "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(leases_tab, "Liste des Locations")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_lease(self):
        """Add a new lease"""
        try:
            # Créer directement la boîte de dialogue sans import
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QLabel, QLineEdit, QPushButton, QComboBox, QDoubleSpinBox, QDateEdit, QTextEdit, QDialogButtonBox, QGroupBox, QSpinBox
            from PyQt5.QtCore import Qt, QDate
            
            # Créer une boîte de dialogue simple
            dialog = QDialog(self)
            dialog.setWindowTitle("Ajouter une Location de Souk")
            dialog.setMinimumWidth(500)
            dialog.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
            dialog.setStyleSheet("QDialog { background-color: #f5f5f5; }")
            
            # Créer le layout principal
            main_layout = QVBoxLayout(dialog)
            
            # Créer le groupe d'informations du souk
            souk_group = QGroupBox("Informations du Souk")
            souk_layout = QFormLayout(souk_group)
            
            # Nom du souk
            name_combo = QComboBox()
            name_combo.addItems([
                "Souk Hebdomadaire", "Souk El Had", "Souk El Tnine", "Souk El Arba", 
                "Souk El Khamis", "Souk El Jomaa", "Souk El Sebt", "Autre"
            ])
            souk_layout.addRow("Nom du Souk:", name_combo)
            
            # Emplacement
            location_edit = QLineEdit()
            location_edit.setPlaceholderText("Ex: Route de Marrakech")
            souk_layout.addRow("Emplacement:", location_edit)
            
            # Superficie
            area_spin = QDoubleSpinBox()
            area_spin.setRange(100, 100000)
            area_spin.setSuffix(" m²")
            area_spin.setValue(1000)
            souk_layout.addRow("Superficie:", area_spin)
            
            # Durée du contrat
            duration_spin = QSpinBox()
            duration_spin.setRange(1, 10)
            duration_spin.setSuffix(" années")
            duration_spin.setValue(1)
            souk_layout.addRow("Durée du Contrat:", duration_spin)
            
            # Montant annuel
            amount_spin = QDoubleSpinBox()
            amount_spin.setRange(1000, 10000000)
            amount_spin.setSuffix(" DH/an")
            amount_spin.setValue(100000)
            souk_layout.addRow("Montant Annuel:", amount_spin)
            
            main_layout.addWidget(souk_group)
            
            # Créer le groupe d'informations du locataire
            lessee_group = QGroupBox("Informations du Locataire")
            lessee_layout = QFormLayout(lessee_group)
            
            # Nom du locataire
            lessee_name_edit = QLineEdit()
            lessee_layout.addRow("Nom Complet:", lessee_name_edit)
            
            # CIN/RC
            lessee_id_edit = QLineEdit()
            lessee_layout.addRow("CIN/RC:", lessee_id_edit)
            
            # Téléphone
            lessee_phone_edit = QLineEdit()
            lessee_layout.addRow("Téléphone:", lessee_phone_edit)
            
            # Adresse
            lessee_address_edit = QLineEdit()
            lessee_layout.addRow("Adresse:", lessee_address_edit)
            
            # Date de début
            start_date_edit = QDateEdit()
            start_date_edit.setCalendarPopup(True)
            start_date_edit.setDate(QDate.currentDate())
            lessee_layout.addRow("Date de Début:", start_date_edit)
            
            main_layout.addWidget(lessee_group)
            
            # Créer les boutons
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            
            main_layout.addWidget(button_box)
            
            # Afficher la boîte de dialogue
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Récupérer les données
                data = {
                    "name": name_combo.currentText(),
                    "location": location_edit.text(),
                    "area": area_spin.value(),
                    "duration": duration_spin.value(),
                    "amount": amount_spin.value(),
                    "lessee_name": lessee_name_edit.text(),
                    "lessee_id": lessee_id_edit.text(),
                    "lessee_phone": lessee_phone_edit.text(),
                    "lessee_address": lessee_address_edit.text(),
                    "start_date": start_date_edit.date().toString("yyyy-MM-dd")
                }
                
                # Vérifier les données
                if not data["location"] or not data["lessee_name"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir tous les champs obligatoires."
                    )
                    return
                
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Location Ajoutée",
                    f"La location du souk {data['name']} à {data['lessee_name']} a été ajoutée avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )