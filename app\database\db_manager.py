#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for the Fiscal Resources Management Application
Handles all database operations and connections
"""

import os
import sqlite3
from datetime import datetime

class DatabaseManager:
    """Database manager class for SQLite operations"""
    
    def __init__(self, db_path="database/fiscal_resources.db"):
        """Initialize database manager"""
        self.db_path = db_path
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.connection.cursor()
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """Close the database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.cursor = None
    
    def execute_query(self, query, params=None):
        """Execute a query with optional parameters"""
        try:
            if not self.connection:
                self.connect()
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Query execution error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            return False
    
    def fetch_all(self, query, params=None):
        """Execute a query and fetch all results"""
        try:
            if not self.connection:
                self.connect()
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Query execution error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            return []
    
    def fetch_one(self, query, params=None):
        """Execute a query and fetch one result"""
        try:
            if not self.connection:
                self.connect()
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Query execution error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            return None
    
    def initialize_database(self):
        """Initialize the database with all required tables"""
        if not os.path.exists(os.path.dirname(self.db_path)):
            os.makedirs(os.path.dirname(self.db_path))
        
        self.connect()
        
        # Create users table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        ''')
        
        # Create configuration table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS configuration (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT NOT NULL,
            UNIQUE(category, key)
        )
        ''')
        
        # Create vehicle types table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS vehicle_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            daily_rate REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create depositor types table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS depositor_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create impound vehicles table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS impound_vehicles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            vehicle_type_id INTEGER NOT NULL,
            license_plate TEXT NOT NULL,
            depositor_type_id INTEGER NOT NULL,
            entry_date DATE NOT NULL,
            status TEXT NOT NULL DEFAULT 'current',
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            exit_date DATE,
            vehicle_condition TEXT,
            auction_number TEXT,
            auction_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_type_id) REFERENCES vehicle_types (id),
            FOREIGN KEY (depositor_type_id) REFERENCES depositor_types (id)
        )
        ''')
        
        # Create sectors table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS sectors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create commercial shops table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS commercial_shops (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sector_id INTEGER NOT NULL,
            shop_number TEXT NOT NULL,
            surface REAL NOT NULL,
            status TEXT NOT NULL DEFAULT 'constructed',
            owner_name TEXT,
            id_number TEXT,
            address TEXT,
            phone TEXT,
            occupation_date DATE,
            monthly_rate REAL NOT NULL,
            last_rate_increase_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sector_id) REFERENCES sectors (id)
        )
        ''')
        
        # Create shop payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS shop_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shop_id INTEGER NOT NULL,
            start_month DATE NOT NULL,
            end_month DATE NOT NULL,
            months_count INTEGER NOT NULL,
            amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (shop_id) REFERENCES commercial_shops (id)
        )
        ''')
        
        # Create souk leases table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS souk_leases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tenant_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            monthly_amount REAL NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create souk payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS souk_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lease_id INTEGER NOT NULL,
            start_month DATE NOT NULL,
            end_month DATE NOT NULL,
            months_count INTEGER NOT NULL,
            amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (lease_id) REFERENCES souk_leases (id)
        )
        ''')
        
        # Create beverage establishments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS beverage_establishments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT NOT NULL UNIQUE,
            establishment_name TEXT NOT NULL,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            patent_number TEXT,
            address TEXT,
            phone TEXT,
            annual_declaration_date DATE,
            status TEXT NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create beverage payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS beverage_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            establishment_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL,
            revenue REAL NOT NULL,
            odp_amount REAL DEFAULT 0,
            declaration_default BOOLEAN DEFAULT FALSE,
            tax_amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (establishment_id) REFERENCES beverage_establishments (id)
        )
        ''')
        
        # Create parking licenses table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS parking_licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_number TEXT NOT NULL UNIQUE,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            vehicle_type_id INTEGER NOT NULL,
            license_plate TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_type_id) REFERENCES vehicle_types (id)
        )
        ''')
        
        # Create parking payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS parking_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL,
            transport_tax REAL NOT NULL,
            parking_fee REAL NOT NULL,
            total_amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (license_id) REFERENCES parking_licenses (id)
        )
        ''')
        
        # Create temporary occupation types table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS occupation_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            rate_per_sqm REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create temporary occupations table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS temporary_occupations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT NOT NULL UNIQUE,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            occupation_type_id INTEGER NOT NULL,
            surface REAL NOT NULL,
            address TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (occupation_type_id) REFERENCES occupation_types (id)
        )
        ''')
        
        # Create occupation payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS occupation_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            occupation_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL,
            amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (occupation_id) REFERENCES temporary_occupations (id)
        )
        ''')
        
        # Create zones table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS zones (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            rate_per_sqm REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create undeveloped lands table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS undeveloped_lands (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT NOT NULL UNIQUE,
            location TEXT NOT NULL,
            lot_number TEXT,
            title_deed TEXT,
            surface REAL NOT NULL,
            zone_id INTEGER NOT NULL,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            acquisition_date DATE,
            status TEXT NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (zone_id) REFERENCES zones (id)
        )
        ''')
        
        # Create land payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS land_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            land_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            amount REAL NOT NULL,
            penalty REAL DEFAULT 0,
            surcharge REAL DEFAULT 0,
            fine REAL DEFAULT 0,
            total_amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (land_id) REFERENCES undeveloped_lands (id)
        )
        ''')
        
        # Create land census table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS land_census (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            location TEXT NOT NULL,
            lot_number TEXT,
            title_deed TEXT,
            surface REAL NOT NULL,
            zone_id INTEGER NOT NULL,
            owner_name TEXT,
            id_number TEXT,
            address TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (zone_id) REFERENCES zones (id)
        )
        ''')
        
        # Insert default admin user if not exists
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = ?", ("admin",))
        if not admin_exists:
            self.execute_query(
                "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
                ("admin", "8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918", "Administrator", "admin")
            )  # Password: admin (SHA-256 hashed)
        
        self.disconnect()