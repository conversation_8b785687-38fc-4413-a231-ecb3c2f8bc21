2025-06-14 19:35:42 - utils.cache - [32mINFO[0m - create_cache:186 - Created cache 'database' with TTL=300.0s, max_size=500
2025-06-14 19:35:42 - utils.cache - [32mINFO[0m - create_cache:186 - Created cache 'ui' with TTL=60.0s, max_size=100
2025-06-14 19:35:42 - utils.cache - [32mINFO[0m - create_cache:186 - Created cache 'config' with TTL=3600.0s, max_size=50
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM users WHERE username = ?...
2025-06-14 19:35:42 - database - DEBUG - wrapper:188 - Query parameters: ('admin',)
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM users WHERE username = ?... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM users WHERE username = ?... completed in 0.001s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM vehicle_types LIMIT 1...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM vehicle_types LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM vehicle_types LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM depositor_types LIMIT 1...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM depositor_types LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM depositor_types LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM sectors LIMIT 1...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM sectors LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM sectors LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM zones LIMIT 1...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM zones LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM zones LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT id FROM occupation_types LIMIT 1...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT id FROM occupation_types LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id FROM occupation_types LIMIT 1... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM impound_vehicles WHERE status = 'current'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM impound_vehicles WHE... completed in 0.001s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM impound_vehicles WHE... completed in 0.001s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM commercial_shops WHERE owner_name IS NOT NULL...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM commercial_shops WHE... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM commercial_shops WHE... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM beverage_establishments WHERE status = 'active'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM beverage_establishme... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM beverage_establishme... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM undeveloped_lands WHERE status = 'active'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM undeveloped_lands WH... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM undeveloped_lands WH... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM impound_vehicles WHERE status = 'pending_payment'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM impound_vehicles WHE... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM impound_vehicles WHE... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM shop_payments WHERE status = 'initiated'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM shop_payments WHERE ... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM shop_payments WHERE ... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM souk_payments WHERE status = 'initiated'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM souk_payments WHERE ... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM souk_payments WHERE ... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM beverage_payments WHERE status = 'initiated'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM beverage_payments WH... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM beverage_payments WH... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM parking_payments WHERE status = 'initiated'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM parking_payments WHE... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM parking_payments WHE... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM occupation_payments WHERE status = 'initiated'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM occupation_payments ... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM occupation_payments ... completed in 0.000s
2025-06-14 19:35:42 - database - DEBUG - wrapper:186 - Executing query: SELECT COUNT(*) as count FROM land_payments WHERE status = 'initiated'...
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - fetch_one: SELECT COUNT(*) as count FROM land_payments WHERE ... completed in 0.000s
2025-06-14 19:35:42 - database - [32mINFO[0m - __exit__:146 - Query: SELECT COUNT(*) as count FROM land_payments WHERE ... completed in 0.000s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT id, username, full_name, role, created_at, last_login FROM users...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT id, username, full_name, role, created_at, ... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id, username, full_name, role, created_at, ... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM vehicle_types...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM vehicle_types... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM vehicle_types... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM depositor_types...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM depositor_types... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM depositor_types... completed in 0.000s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM sectors...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM sectors... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM sectors... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM occupation_types...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM occupation_types... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM occupation_types... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT * FROM zones...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT * FROM zones... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT * FROM zones... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: 
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
   ...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.002s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.003s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: 
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
   ...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: 
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
   ...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.001s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: 
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
   ...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: 
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
   ...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: 
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
   ...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.000s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: 
            SELECT v.*, vt.name_fr as vehicle_typ... completed in 0.001s
2025-06-14 19:35:43 - database - DEBUG - wrapper:186 - Executing query: SELECT id, name_fr FROM sectors ORDER BY name_fr...
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - fetch_all: SELECT id, name_fr FROM sectors ORDER BY name_fr... completed in 0.001s
2025-06-14 19:35:43 - database - [32mINFO[0m - __exit__:146 - Query: SELECT id, name_fr FROM sectors ORDER BY name_fr... completed in 0.001s
2025-06-14 19:35:45 - theme - [32mINFO[0m - set_theme:177 - Theme changed to: light
2025-06-14 19:35:45 - theme - [32mINFO[0m - set_font_size:207 - Font size changed to: normal (14px)
2025-06-14 19:35:45 - theme - [32mINFO[0m - initialize_theme:347 - Theme manager initialized
