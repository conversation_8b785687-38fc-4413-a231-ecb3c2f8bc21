#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Undeveloped lands management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class LandsWidget(QWidget):
    """Undeveloped lands management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize lands widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Gestion des Taxes sur les Terrains Urbains Non Bâtis")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add land button
        add_land_btn = QPushButton("Ajouter un Terrain")
        add_land_btn.clicked.connect(self.add_land)
        header_layout.addWidget(add_land_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create lands tab
        lands_tab = QWidget()
        lands_layout = QVBoxLayout(lands_tab)
        
        # Create lands table
        self.lands_table = QTableWidget()
        self.lands_table.setColumnCount(12)
        self.lands_table.setHorizontalHeaderLabels([
            "N° Dossier", "Emplacement", "N° Lot", "Titre foncier", 
            "Surface", "Zone", "Propriétaire", "CIN/RC", 
            "Adresse", "Téléphone", "Dernière année", "Total"
        ])
        self.lands_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.lands_table.verticalHeader().setVisible(False)
        
        lands_layout.addWidget(self.lands_table)
        
        # Create payment tab
        payment_tab = QWidget()
        payment_layout = QVBoxLayout(payment_tab)
        
        # TODO: Add payment interface
        payment_layout.addWidget(QLabel("Interface de paiement à implémenter"))
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "N° Dossier", "Emplacement", "N° Lot", "Propriétaire", 
            "CIN/RC", "Année", "Montant", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(10)
        self.history_table.setHorizontalHeaderLabels([
            "N° Dossier", "Emplacement", "N° Lot", "Titre foncier", 
            "Surface", "Propriétaire", "CIN/RC", "Période", 
            "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Create census tab
        census_tab = QWidget()
        census_layout = QVBoxLayout(census_tab)
        
        # Create census table
        self.census_table = QTableWidget()
        self.census_table.setColumnCount(10)
        self.census_table.setHorizontalHeaderLabels([
            "Emplacement", "N° Lot", "Titre foncier", "Surface", 
            "Zone", "Propriétaire", "CIN/RC", "Adresse", 
            "Téléphone", "Actions"
        ])
        self.census_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.census_table.verticalHeader().setVisible(False)
        
        census_layout.addWidget(self.census_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(lands_tab, "Liste des Terrains")
        self.tab_widget.addTab(payment_tab, "Initiation Paiement")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        self.tab_widget.addTab(census_tab, "Recensements")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_land(self):
        """Add a new land"""
        try:
            from app.ui.dialogs import LandDialog, ensure_app_imports
            
            # Ensure proper imports
            ensure_app_imports()
            
            dialog = LandDialog(self.db_manager, self)
            result = dialog.exec_()
            
            if result == LandDialog.Accepted:
                data = dialog.get_land_data()
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Terrain Ajouté",
                    f"Le terrain situé à {data['location']} a été ajouté avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )