#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Authentication utilities for the application
Handles user authentication and session management
"""

import hashlib
import datetime
import bcrypt
import secrets
from typing import Optional, Dict, Any
from database.db_manager import DatabaseManager

class Auth:
    """Authentication manager class"""
    
    def __init__(self, db_manager):
        """Initialize authentication manager"""
        self.db_manager = db_manager
        self.current_user = None
    
    def login(self, username: str, password: str) -> bool:
        """Authenticate a user with improved security"""
        try:
            # Query the database for user
            user = self.db_manager.fetch_one(
                "SELECT * FROM users WHERE username = ?",
                (username,)
            )

            if not user:
                return False

            # Check password using bcrypt if available, fallback to SHA-256
            stored_password = user["password"]

            # Check if password is bcrypt hashed (starts with $2b$)
            if stored_password.startswith('$2b$'):
                # Use bcrypt verification
                if bcrypt.checkpw(password.encode('utf-8'), stored_password.encode('utf-8')):
                    password_valid = True
                else:
                    password_valid = False
            else:
                # Legacy SHA-256 verification
                hashed_password = hashlib.sha256(password.encode()).hexdigest()
                password_valid = (stored_password == hashed_password)

                # Upgrade to bcrypt if login successful
                if password_valid:
                    self._upgrade_password_hash(user["id"], password)

            if password_valid:
                # Update last login timestamp
                self.db_manager.execute_query(
                    "UPDATE users SET last_login = ? WHERE id = ?",
                    (datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), user["id"])
                )

                # Set current user
                self.current_user = dict(user)
                return True

            return False

        except Exception as e:
            print(f"Login error: {e}")
            return False
    
    def logout(self):
        """Log out the current user"""
        self.current_user = None
        return True
    
    def is_authenticated(self):
        """Check if a user is authenticated"""
        return self.current_user is not None
    
    def get_current_user(self):
        """Get the current authenticated user"""
        return self.current_user
    
    def has_permission(self, permission):
        """Check if the current user has a specific permission"""
        if not self.is_authenticated():
            return False
        
        # Admin has all permissions
        if self.current_user["role"] == "admin":
            return True
        
        # Check specific permissions based on role
        if permission == "view_dashboard":
            return True  # All authenticated users can view dashboard
        
        if permission == "manage_users":
            return self.current_user["role"] == "admin"
        
        if permission == "validate_payment":
            return self.current_user["role"] in ["admin", "regisseur"]
        
        if permission == "initiate_payment":
            return self.current_user["role"] in ["admin", "regisseur", "caissier", "employee"]
        
        if permission == "edit_records":
            return self.current_user["role"] in ["admin", "employee"]
        
        # Default to false for unknown permissions
        return False
    
    def change_password(self, user_id, current_password, new_password):
        """Change a user's password"""
        # Hash the passwords
        hashed_current = hashlib.sha256(current_password.encode()).hexdigest()
        hashed_new = hashlib.sha256(new_password.encode()).hexdigest()
        
        # Verify current password
        user = self.db_manager.fetch_one(
            "SELECT id FROM users WHERE id = ? AND password = ?",
            (user_id, hashed_current)
        )
        
        if not user:
            return False
        
        # Update password
        result = self.db_manager.execute_query(
            "UPDATE users SET password = ? WHERE id = ?",
            (hashed_new, user_id)
        )
        
        return result
    
    def _upgrade_password_hash(self, user_id: int, password: str) -> None:
        """Upgrade legacy SHA-256 password to bcrypt"""
        try:
            # Generate bcrypt hash
            salt = bcrypt.gensalt()
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)

            # Update in database
            self.db_manager.execute_query(
                "UPDATE users SET password = ? WHERE id = ?",
                (hashed_password.decode('utf-8'), user_id)
            )
        except Exception as e:
            print(f"Password upgrade error: {e}")

    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        try:
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
        except Exception:
            # Fallback to SHA-256 if bcrypt fails
            return hashlib.sha256(password.encode()).hexdigest()

    def create_user(self, username: str, password: str, full_name: str, role: str) -> bool:
        """Create a new user with improved security"""
        try:
            # Validate input
            if not username or not password or not full_name or not role:
                return False

            # Check if username already exists
            existing_user = self.db_manager.fetch_one(
                "SELECT id FROM users WHERE username = ?",
                (username,)
            )

            if existing_user:
                return False

            # Hash the password
            hashed_password = self._hash_password(password)

            # Create the user
            result = self.db_manager.execute_query(
                "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
                (username, hashed_password, full_name, role)
            )

            return result

        except Exception as e:
            print(f"User creation error: {e}")
            return False
    
    def update_user(self, user_id, full_name, role):
        """Update a user's information"""
        result = self.db_manager.execute_query(
            "UPDATE users SET full_name = ?, role = ? WHERE id = ?",
            (full_name, role, user_id)
        )
        
        return result
    
    def delete_user(self, user_id):
        """Delete a user"""
        result = self.db_manager.execute_query(
            "DELETE FROM users WHERE id = ?",
            (user_id,)
        )
        
        return result
    
    def get_all_users(self):
        """Get all users"""
        users = self.db_manager.fetch_all(
            "SELECT id, username, full_name, role, created_at, last_login FROM users"
        )
        
        return users