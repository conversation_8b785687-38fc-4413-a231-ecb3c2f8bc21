#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Beverage establishments management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class BeveragesWidget(QWidget):
    """Beverage establishments management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize beverages widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Gestion des Taxes sur les Débits de Boissons")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add establishment button
        add_establishment_btn = QPushButton("Ajouter un Établissement")
        add_establishment_btn.clicked.connect(self.add_establishment)
        header_layout.addWidget(add_establishment_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create establishments tab
        establishments_tab = QWidget()
        establishments_layout = QVBoxLayout(establishments_tab)
        
        # Create establishments table
        self.establishments_table = QTableWidget()
        self.establishments_table.setColumnCount(11)
        self.establishments_table.setHorizontalHeaderLabels([
            "N° Dossier", "Établissement", "Propriétaire", "CIN/RC", 
            "N° Patente", "Adresse", "Téléphone", "Déclaration annuelle", 
            "Dernier trimestre", "Chiffre d'affaires", "Total"
        ])
        self.establishments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.establishments_table.verticalHeader().setVisible(False)
        
        establishments_layout.addWidget(self.establishments_table)
        
        # Create payment tab
        payment_tab = QWidget()
        payment_layout = QVBoxLayout(payment_tab)
        
        # TODO: Add payment interface
        payment_layout.addWidget(QLabel("Interface de paiement à implémenter"))
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "N° Dossier", "Établissement", "Propriétaire", "CIN/RC", 
            "Période", "Montant", "N° Créance", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(7)
        self.history_table.setHorizontalHeaderLabels([
            "N° Dossier", "Établissement", "Propriétaire", "CIN/RC", 
            "Période", "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Create cessations tab
        cessations_tab = QWidget()
        cessations_layout = QVBoxLayout(cessations_tab)
        
        # Create cessations table
        self.cessations_table = QTableWidget()
        self.cessations_table.setColumnCount(8)
        self.cessations_table.setHorizontalHeaderLabels([
            "N° Dossier", "Établissement", "Propriétaire", "CIN/RC", 
            "Adresse", "Téléphone", "Date de cessation", "Actions"
        ])
        self.cessations_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.cessations_table.verticalHeader().setVisible(False)
        
        cessations_layout.addWidget(self.cessations_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(establishments_tab, "Liste des Établissements")
        self.tab_widget.addTab(payment_tab, "Initiation Paiement")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        self.tab_widget.addTab(cessations_tab, "Cessations d'Activité")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_establishment(self):
        """Add a new establishment"""
        try:
            from app.ui.dialogs import BeverageDialog, ensure_app_imports
            
            # Ensure proper imports
            ensure_app_imports()
            
            dialog = BeverageDialog(self.db_manager, self)
            result = dialog.exec_()
            
            if result == BeverageDialog.Accepted:
                data = dialog.get_beverage_data()
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Établissement Ajouté",
                    f"L'établissement {data['name']} a été ajouté avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )