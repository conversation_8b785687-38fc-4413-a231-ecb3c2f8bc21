Application de Gestion des Ressources Fiscales Communales
Objectif du Projet:
Développer une Application de bureau Python avec SQLite complète pour la gestion des ressources fiscales municipales (taxes, redevances, revenus locatifs). L'objectif est de rationaliser les opérations, d'améliorer la gestion des données et de renforcer les capacités de reporting pour la commune.

Exigences Linguistiques:
L'application doit être entièrement bilingue (Arabe et Français), avec la possibilité de basculer entre les deux langues à tout moment via l'interface utilisateur.

Fonctionnalités Clés et Modules:

1. Système d'Authentification et de Gestion des Utilisateurs
Page de connexion sécurisée avec différents niveaux d'accès (Administrateur, Employé, etc.).

Gestion des sessions utilisateurs.

Tableau de bord personnalisé en fonction du rôle de l'utilisateur.

2. Interface Utilisateur (UI)
Menu vertical réactif avec les modules principaux.

Fonction de recherche globale dans tous les modules, ainsi que dans la base de données du logiciel "GEC" (Gestion Eau Communale).

Page d'administration dédiée pour la configuration des différentes fonctionnalités de l'application (ex: types de véhicules, types de déposants, tarifs de location, pourcentages de taxes).

3. Modules de Gestion Fiscale
3.1. Gestion des Droits de Fourrière
Liste des Véhicules en Fourrière (Actuels):

Tableau affichant: Type de véhicule (configurable via admin), Numéro d'immatriculation, Déposant (configurable via admin), Date d'entrée, Jours (calculés entre date d'entrée et date actuelle), Total (tarif du type de véhicule * nombre de jours).

Actions disponibles: Modifier, Supprimer, Initier Paiement.

Bouton "Initier Paiement" ouvrant un modal pour la saisie du numéro de la créance. Après validation, le véhicule est déplacé vers la "Liste des Paiements en Attente".

Liste des Paiements en Attente (Droits de Fourrière):

Ce tableau liste les véhicules pour lesquels un paiement a été initié, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les informations du véhicule et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, le véhicule est déplacé vers la liste "En Attente de Sortie".

Liste des Véhicules en Attente de Sortie:

Affiche les véhicules ayant payé leur séjour, avec ajout de colonnes pour le numéro de quittance et la date de paiement.

Le bouton "Sortie" lance un modal de confirmation avec la date de sortie.

Liste des Véhicules Sortis:

Contient les véhicules sortis avec toutes les informations de paiement et la date de sortie.

Liste des Véhicules Dépassant 365 Jours:

Déplacement automatique des véhicules ayant dépassé 365 jours en fourrière, avec une colonne indiquant l'état du véhicule (bon, moyen, mauvais).

Liste des Véhicules à Vendre aux Enchères:

Affiche les véhicules destinés à la vente aux enchères, avec possibilité de les regrouper par numéro et date de vente.

Recherche et Filtrage: Par type et par date.

Rapports: Possibilité de télécharger les tableaux en formats Excel et PDF.

3.2. Gestion des Produits de Location des Locaux Commerciaux
Liste des Boutiques:

Tableau affichant: Secteur (configurable via admin), Numéro de boutique, Surfaces, État (construit, non construit), Nom et prénom du propriétaire, CIN/RC, Adresse, Numéro de téléphone, Date d'occupation, Mois non payés, Nombre de mois, Tarif (configurable via admin, avec augmentation de 10% tous les 3 ans), Total (nombre de mois * tarif).

Actions: Modifier, Supprimer.

Impression groupée des avis de non-paiement.

Initiation Paiement (Onglet par défaut):

Zone de recherche par secteur, CIN/RC, Nom et prénom du propriétaire.

Tableau affichant les résultats de recherche et toutes les boutiques de la même personne (selon CIN/RC).

Détails affichés: Secteur, Numéro de boutique, Propriétaire, Dernier mois non payé, Nombre de mois, Tarif, Total.

Inputs pour saisir les mois souhaités à payer, le total par boutique, et le total général.

Bouton "Initier Paiement" ouvrant un modal pour la saisie du numéro de la créance. Après validation, la transaction est déplacée vers la "Liste des Paiements en Attente".

Tableau affichant l'historique des paiements pour la boutique sélectionnée.

Liste des Paiements en Attente (Locaux Commerciaux):

Ce tableau liste les transactions de paiement initiées, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les détails de la boutique et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, la transaction est déplacée vers la "Liste Historique de Paiement".

Liste Historique de Paiement:

Tableau contenant: Secteur, Numéro de boutique, Nom et prénom du propriétaire, CIN/RC, Période payée (ex: du 07/2022 au 12/2022), Nombre de mois payés (ex: 6 mois), Total payé, Quittance, Date de paiement.

Actions: Modifier, Supprimer.

Liste des Boutiques Non Exploitées:

Tableau affichant: Secteur, Numéro de boutique, Surfaces, État (construit, non construit).

Actions: Affecter un propriétaire, Modifier, Supprimer.

Rapports & Recherche: Téléchargement des tableaux en Excel et PDF; zone de recherche et de filtrage.

3.3. Produit d'Affermage des Souks Communaux
Liste des Locations d'Affermage:

Tableau affichant: Locataire, CIN/RC, Adresse, Téléphone, Montant de location, Dernier mois non payé, Total, Date de début et de fin de location.

Actions: Modifier, Supprimer, Initier Paiement.

Impression des avis de non-paiement.

Liste des Paiements en Attente (Affermage des Souks):

Ce tableau liste les transactions de paiement initiées, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les détails de la location et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, la transaction est déplacée vers la "Liste Historique des Paiements".

Liste Historique des Paiements:

Tableau contenant: Locataire, CIN/RC, Période payée, Total, Numéro de quittance, Date de paiement.

Actions: Modifier, Supprimer.

3.4. Gestion des Taxes sur les Débits de Boissons
Liste des Débits de Boisson:

Tableau affichant: Numéro de Dossier, Nom de l'établissement, Propriétaire, CIN/RC, Numéro de Patente, Adresse, Téléphone, Date de déclaration annuelle, Dernier trimestre non payé, Estimation du chiffre d'affaires, Total (10% du chiffre d'affaires, configurable via admin).

Actions: Modification, Suppression, Cessation.

Impression groupée des avis de non-paiement.

Initiation Paiement (Onglet par défaut):

Zone de recherche par numéro de dossier, CIN/RC, établissement, propriétaire.

Affichage des informations de recherche (Dossier N°, Nom de l'établissement, Propriétaire, Adresse, CIN/RC, Tél).

Boutons de génération d'imprimés pour la déclaration de chiffre d'affaires et la déclaration annuelle.

Tableau affichant les trimestres non payés avec: Année, Trimestre, Chiffre d'affaires, ODP (Occupation du Domaine Public - si l'établissement est dans la liste des redevances d'occupation temporaire), Défaut de déclaration, Total (calculé à 10% du chiffre d'affaires).

Bouton "Initier Paiement" ouvrant un modal pour la saisie du numéro de la créance. Après validation, la transaction est déplacée vers la "Liste des Paiements en Attente".

Liste des historiques des paiements pour l'établissement sélectionné.

Liste des Paiements en Attente (Débits de Boissons):

Ce tableau liste les transactions de paiement initiées, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les détails de l'établissement et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, la transaction est déplacée vers la "Liste des Historiques des Paiements".

Liste des Historiques des Paiements:

Tableau affichant: Numéro de Dossier, Nom de l'établissement, Propriétaire, CIN/RC, Période payée, Quittance, Date de paiement.

Liste des Cessations d'Activité:

Tableau affichant les établissements ayant cessé leur activité.

Possibilité d'imprimer le formulaire de cessation d'activité avec les informations de l'établissement.

3.5. Gestion du Droit de Stationnement et de la Taxe sur le Transport Public
Liste du Droit de Stationnement:

Tableau affichant: Numéro d'agrément, Propriétaire (nom et prénom), CIN/RC, Type (configurable via admin), Numéro d'immatriculation, Adresse, Téléphone, Dernier trimestre non payé, Total.

Actions: Modification, Suppression.

Téléchargement des tableaux en PDF et Excel.

Impression groupée des avis de non-paiement.

Initiation Paiement (Onglet par défaut):

Zone de recherche par numéro d'agrément, CIN/RC, propriétaire.

Affichage des informations de recherche (Numéro d'agrément, Propriétaire, Adresse, CIN/RC, Tél).

Tableau affichant les trimestres non payés avec: Année, Trimestres, Taxe sur le transport public des voyageurs, Droit de stationnement, Total.

Bouton "Initier Paiement" ouvrant un modal pour la saisie du numéro de la créance. Après validation, la transaction est déplacée vers la "Liste des Paiements en Attente".

Liste des historiques des paiements pour le numéro d'agrément sélectionné.

Liste des Paiements en Attente (Droit de Stationnement):

Ce tableau liste les transactions de paiement initiées, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les détails de la licence et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, la transaction est déplacée vers la "Liste des Historiques des Paiements".

Liste des Historiques des Paiements:

Tableau affichant: Numéro d'agrément, Propriétaire, CIN/RC, Période payée, Quittance, Date de paiement.

3.6. Gestion de la Redevance d'Occupation Temporaire
Liste des Redevances d'Occupation Temporaire:

Tableau affichant: Numéro de dossier, Propriétaire (nom et prénom), CIN/RC, Type (configurable via admin), Surface, Adresse, Téléphone, Dernier trimestre non payé, Total.

Actions: Modification, Suppression.

Téléchargement des tableaux en PDF et Excel.

Impression groupée des avis de non-paiement.

Initiation Paiement (Onglet par défaut):

Zone de recherche par numéro d'agrément, CIN/RC, Propriétaire.

Affichage des informations de recherche (Numéro de dossier, Surface, Propriétaire, Adresse, CIN/RC, Tél).

Tableau affichant les trimestres non payés avec: Année, Trimestres, Type, Surface, Total.

Bouton "Initier Paiement" ouvrant un modal pour la saisie du numéro de la créance. Après validation, la transaction est déplacée vers la "Liste des Paiements en Attente".

Liste des historiques des paiements pour le numéro de dossier sélectionné.

Liste des Paiements en Attente (Redevance d'Occupation Temporaire):

Ce tableau liste les transactions de paiement initiées, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les détails du dossier et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, la transaction est déplacée vers la "Liste des Historiques des Paiements".

Liste des Historiques des Paiements:

Tableau affichant: Numéro de dossier, Propriétaire, CIN/RC, Période payée, Quittance, Date de paiement.

3.7. Gestion des Taxes sur les Terrains Urbains Non Bâtis
Liste des Terrains Urbains Non Bâtis:

Tableau affichant: Numéro de dossier, Emplacement, Numéro de Lot, Titre foncier, Surfaces, Zone (logement individuel, immeubles, etc.), Nom et prénom du propriétaire, CIN/RC, Adresse, Numéro de téléphone, Date d'acquisition, Dernière année non payée, Total.

Actions: Modifier, Supprimer, Changer état ou propriétaire.

Impression des formulaires de déclaration annuelle et de transfert/changement.

Impression groupée des avis de non-paiement.

Calcul du total: surface * tarif (par type).

Date limite de règlement et de déclaration: 28 février de chaque année.

Pénalités: 10% du montant principal; Majoration: 5% pour le premier mois de retard + 0.5% pour les mois suivants; Amende: 10% du montant principal (> 500 DH minimum).

Initiation Paiement (Onglet par défaut):

Zone de recherche par numéro de Lot, Titre foncier, Emplacement, CIN/RC, Nom et prénom.

Tableau affichant les résultats de recherche et tous les lots de la même personne (selon CIN/RC).

Bouton "Initier Paiement" ouvrant un modal pour la saisie du numéro de la créance. Après validation, la transaction est déplacée vers la "Liste des Paiements en Attente".

Tableau affichant l'historique des paiements pour chaque lot sélectionné.

Liste des Paiements en Attente (Terrains Urbains Non Bâtis):

Ce tableau liste les transactions de paiement initiées, en attente de validation par l'utilisateur "caissier" ou "régisseur".

Chaque entrée contient les détails du terrain et le numéro de la créance.

Action: "Valider Paiement" (bouton disponible pour le "régisseur") ouvrant un modal pour la saisie du numéro de quittance et la date de paiement. Après validation, la transaction est déplacée vers la "Liste Historique de Paiement".

Liste Historique de Paiement:

Tableau contenant: Numéro de dossier, Emplacement, Numéro de Lot, Titre foncier, Surfaces, Nom et prénom du propriétaire, CIN/RC, Total payé, Période, Quittance, Date de paiement.

Actions: Modifier, Supprimer.

Liste des Recensements:

Tableau affichant: Emplacement, Numéro de Lot, Titre foncier, Surfaces, Zone, Nom et prénom du propriétaire, CIN/RC, Adresse, Numéro de téléphone, Total (calculé depuis 2022).

Actions: Modifier, Supprimer, Transférer vers la liste principale.

Impression des avis de non-paiement par lots.

Téléchargement des tableaux en Excel et PDF; zone de recherche et de filtrage.

4. Sécurité et Performance
Chiffrement des données sensibles.

Gestion précise des autorisations basée sur les rôles.

Compatibilité avec les navigateurs modernes et les différents appareils.

5. Fonctionnalités Communes à Tous les Modules
Ajout, modification et suppression d'entrées (selon les droits de l'utilisateur).

Génération d'avis de non-paiement.

Historique des paiements.

Génération de rapports personnalisables aux formats PDF et Excel.

Tableaux de bord avec indicateurs clés de performance (KPIs).