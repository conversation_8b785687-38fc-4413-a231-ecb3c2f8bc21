#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Commercial shops management module for the Fiscal Resources Management Application
Enhanced with base classes, caching, and improved performance
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QSplitter, QGroupBox, QFormLayout, QLineEdit, QComboBox, QSpinBox
)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont

from ui.base_widgets import BaseModuleWidget, BaseTableWidget, SearchWidget
from ui.dialogs import ShopDialog
from utils.logging_config import get_logger, PerformanceTimer
from utils.cache import cache_key_for_module_data
from typing import Dict, List, Any, Optional

class ShopsWidget(BaseModuleWidget):
    """Enhanced commercial shops management widget"""

    def __init__(self, db_manager, auth):
        """Initialize shops widget"""
        self.logger = get_logger("shops")
        super().__init__(db_manager, auth, "Gestion des Locaux Commerciaux")
    
    def create_action_buttons(self, layout: QHBoxLayout):
        """Create action buttons for shops module"""
        # Add shop button
        add_shop_btn = QPushButton("Ajouter une Boutique")
        add_shop_btn.clicked.connect(self.add_shop)
        layout.addWidget(add_shop_btn)

        # Export button
        export_btn = QPushButton("Exporter")
        export_btn.clicked.connect(self.export_data)
        layout.addWidget(export_btn)

        # Refresh button (from base class)
        super().create_action_buttons(layout)

    def create_content(self):
        """Create main content area with enhanced features"""
        # Create splitter for search and content
        splitter = QSplitter(Qt.Horizontal)

        # Create search panel
        search_panel = self.create_search_panel()
        splitter.addWidget(search_panel)

        # Create tab widget for different views
        self.tab_widget = QTabWidget()
        self.create_tabs()
        splitter.addWidget(self.tab_widget)

        # Set splitter proportions
        splitter.setSizes([300, 900])

        self.main_layout.addWidget(splitter)

    def create_search_panel(self) -> QWidget:
        """Create search panel with filters"""
        search_fields = [
            {'name': 'shop_number', 'label': 'N° Boutique', 'type': 'text'},
            {'name': 'owner_name', 'label': 'Propriétaire', 'type': 'text'},
            {'name': 'owner_id', 'label': 'CIN/RC', 'type': 'text'},
            {'name': 'sector_id', 'label': 'Secteur', 'type': 'combo', 'options': self.get_sector_options()},
            {'name': 'status', 'label': 'État', 'type': 'combo', 'options': [
                {'label': 'Tous', 'value': ''},
                {'label': 'Occupé', 'value': 'occupied'},
                {'label': 'Libre', 'value': 'free'},
                {'label': 'En construction', 'value': 'under_construction'}
            ]}
        ]

        self.search_widget = SearchWidget(search_fields)
        self.search_widget.search_requested.connect(self.perform_search)
        self.search_widget.search_cleared.connect(self.clear_search)

        return self.search_widget

    def create_tabs(self):
        """Create tab widgets for different shop views"""
        # All shops tab
        self.create_all_shops_tab()

        # Payment initiation tab
        self.create_payment_tab()

        # Pending payments tab
        self.create_pending_payments_tab()

        # Payment history tab
        self.create_payment_history_tab()

        # Unoccupied shops tab
        self.create_unoccupied_shops_tab()

    def create_all_shops_tab(self):
        """Create tab for all shops"""
        all_shops_widget = QWidget()
        layout = QVBoxLayout(all_shops_widget)

        # Table columns
        columns = [
            'Secteur', 'N° Boutique', 'Surface (m²)', 'État', 'Propriétaire',
            'CIN/RC', 'Téléphone', 'Date d\'occupation', 'Mois impayés', 'Total (DH)'
        ]

        self.all_shops_table = BaseTableWidget(columns)
        self.all_shops_table.row_double_clicked.connect(self.edit_shop)

        # Add action buttons
        self.all_shops_table.add_action_column('Actions', 'Modifier', self.edit_shop)

        layout.addWidget(self.all_shops_table)

        self.tab_widget.addTab(all_shops_widget, "Toutes les Boutiques")

    def create_payment_tab(self):
        """Create payment initiation tab"""
        payment_widget = QWidget()
        layout = QVBoxLayout(payment_widget)

        # Payment search and initiation area
        payment_group = QGroupBox("Initiation de Paiement")
        payment_layout = QVBoxLayout(payment_group)

        # Search form
        search_form = QFormLayout()

        self.payment_search_owner = QLineEdit()
        self.payment_search_owner.setPlaceholderText("Nom du propriétaire")
        search_form.addRow("Propriétaire:", self.payment_search_owner)

        self.payment_search_cin = QLineEdit()
        self.payment_search_cin.setPlaceholderText("CIN/RC")
        search_form.addRow("CIN/RC:", self.payment_search_cin)

        search_btn = QPushButton("Rechercher")
        search_btn.clicked.connect(self.search_for_payment)
        search_form.addRow("", search_btn)

        payment_layout.addLayout(search_form)

        # Results table
        payment_columns = [
            'Secteur', 'N° Boutique', 'Propriétaire', 'Dernier mois payé',
            'Mois impayés', 'Tarif mensuel', 'Total'
        ]

        self.payment_table = BaseTableWidget(payment_columns)
        self.payment_table.add_action_column('Action', 'Initier Paiement', self.initiate_payment)

        payment_layout.addWidget(self.payment_table)
        layout.addWidget(payment_group)

        self.tab_widget.addTab(payment_widget, "Initiation Paiement")

    def create_pending_payments_tab(self):
        """Create pending payments tab"""
        pending_widget = QWidget()
        layout = QVBoxLayout(pending_widget)

        pending_columns = [
            'Secteur', 'N° Boutique', 'Propriétaire', 'CIN/RC',
            'Période', 'Montant', 'N° Créance', 'Date création'
        ]

        self.pending_payments_table = BaseTableWidget(pending_columns)

        # Add validation action for authorized users
        if self.auth.has_permission('validate_payment'):
            self.pending_payments_table.add_action_column('Action', 'Valider', self.validate_payment)

        layout.addWidget(self.pending_payments_table)

        self.tab_widget.addTab(pending_widget, "Paiements en Attente")

    def create_payment_history_tab(self):
        """Create payment history tab"""
        history_widget = QWidget()
        layout = QVBoxLayout(history_widget)

        history_columns = [
            'Secteur', 'N° Boutique', 'Propriétaire', 'CIN/RC',
            'Période payée', 'Mois payés', 'Total payé', 'N° Quittance', 'Date paiement'
        ]

        self.payment_history_table = BaseTableWidget(history_columns)
        self.payment_history_table.add_action_column('Actions', 'Voir détails', self.view_payment_details)

        layout.addWidget(self.payment_history_table)

        self.tab_widget.addTab(history_widget, "Historique Paiements")

    def create_unoccupied_shops_tab(self):
        """Create unoccupied shops tab"""
        unoccupied_widget = QWidget()
        layout = QVBoxLayout(unoccupied_widget)

        unoccupied_columns = [
            'Secteur', 'N° Boutique', 'Surface (m²)', 'État'
        ]

        self.unoccupied_table = BaseTableWidget(unoccupied_columns)
        self.unoccupied_table.add_action_column('Actions', 'Affecter', self.assign_owner)

        layout.addWidget(self.unoccupied_table)

        self.tab_widget.addTab(unoccupied_widget, "Boutiques Non Exploitées")

    # Data loading methods
    def refresh_data(self):
        """Refresh all shop data"""
        super().refresh_data()

        with PerformanceTimer("Loading all shops data", self.logger):
            self.load_all_shops()
            self.load_pending_payments()
            self.load_payment_history()
            self.load_unoccupied_shops()

    def load_all_shops(self):
        """Load all shops data with caching"""
        def fetch_shops():
            return self.db_manager.fetch_all('''
                SELECT s.*, sec.name_fr as sector_name,
                       COALESCE(MAX(sp.end_month), s.occupation_date) as last_paid_month,
                       CASE
                           WHEN MAX(sp.end_month) IS NULL THEN
                               CAST((julianday('now') - julianday(s.occupation_date)) / 30 AS INTEGER)
                           ELSE
                               CAST((julianday('now') - julianday(MAX(sp.end_month))) / 30 AS INTEGER)
                       END as unpaid_months,
                       (CASE
                           WHEN MAX(sp.end_month) IS NULL THEN
                               CAST((julianday('now') - julianday(s.occupation_date)) / 30 AS INTEGER)
                           ELSE
                               CAST((julianday('now') - julianday(MAX(sp.end_month))) / 30 AS INTEGER)
                       END * s.monthly_rate) as total_due
                FROM commercial_shops s
                JOIN sectors sec ON s.sector_id = sec.id
                LEFT JOIN shop_payments sp ON s.id = sp.shop_id AND sp.status = 'validated'
                GROUP BY s.id
                ORDER BY s.shop_number
            ''')

        cache_key = cache_key_for_module_data('shops', 'all_shops')
        shops_data = self.get_cached_data(cache_key, fetch_shops)

        # Transform data for table display
        table_data = []
        for shop in shops_data:
            table_data.append({
                'Secteur': shop['sector_name'],
                'N° Boutique': shop['shop_number'],
                'Surface (m²)': f"{shop['surface']:.1f}",
                'État': shop['status'],
                'Propriétaire': shop['owner_name'] or 'Non affecté',
                'CIN/RC': shop['id_number'] or '',
                'Téléphone': shop['phone'] or '',
                'Date d\'occupation': shop['occupation_date'] or '',
                'Mois impayés': shop['unpaid_months'] or 0,
                'Total (DH)': f"{shop['total_due']:.2f}" if shop['total_due'] else "0.00"
            })

        self.all_shops_table.load_data(table_data)

    def load_pending_payments(self):
        """Load pending payments"""
        # Implementation similar to load_all_shops
        pass

    def load_payment_history(self):
        """Load payment history"""
        # Implementation similar to load_all_shops
        pass

    def load_unoccupied_shops(self):
        """Load unoccupied shops"""
        # Implementation similar to load_all_shops
        pass

    # Helper methods
    def get_sector_options(self) -> List[Dict[str, str]]:
        """Get sector options for search combo"""
        sectors = self.db_manager.fetch_all("SELECT id, name_fr FROM sectors ORDER BY name_fr")
        options = [{'label': 'Tous', 'value': ''}]
        for sector in sectors:
            options.append({'label': sector['name_fr'], 'value': str(sector['id'])})
        return options

    # Action methods
    @pyqtSlot()
    def add_shop(self):
        """Add a new shop"""
        try:
            dialog = ShopDialog(self.db_manager, self)
            result = dialog.exec_()

            if result == ShopDialog.Accepted:
                shop_data = dialog.get_shop_data()

                # Save to database
                shop_id = self.db_manager.create_shop(shop_data)

                if shop_id:
                    self.show_success(
                        "Boutique Ajoutée",
                        f"La boutique {shop_data['shop_number']} a été ajoutée avec succès."
                    )
                    # Invalidate cache and refresh
                    self.db_manager.invalidate_table_cache('commercial_shops')
                    self.refresh_data()
                else:
                    self.show_error(
                        "Erreur",
                        "Impossible d'ajouter la boutique. Vérifiez que le numéro n'existe pas déjà."
                    )

        except Exception as e:
            self.show_error("Erreur", f"Une erreur s'est produite: {str(e)}")
            self.logger.error(f"Error adding shop: {e}")

    @pyqtSlot(dict)
    def edit_shop(self, shop_data: Dict[str, Any]):
        """Edit an existing shop"""
        try:
            dialog = ShopDialog(self.db_manager, self, shop_data)
            result = dialog.exec_()

            if result == ShopDialog.Accepted:
                updated_data = dialog.get_shop_data()

                # Update in database
                success = self.db_manager.update_shop(shop_data['id'], updated_data)

                if success:
                    self.show_success(
                        "Boutique Modifiée",
                        f"La boutique {updated_data['shop_number']} a été modifiée avec succès."
                    )
                    # Invalidate cache and refresh
                    self.db_manager.invalidate_table_cache('commercial_shops')
                    self.refresh_data()
                else:
                    self.show_error("Erreur", "Impossible de modifier la boutique.")

        except Exception as e:
            self.show_error("Erreur", f"Une erreur s'est produite: {str(e)}")
            self.logger.error(f"Error editing shop: {e}")

    @pyqtSlot()
    def perform_search(self, search_params: Dict[str, Any]):
        """Perform search with given parameters"""
        # Implementation for search functionality
        self.logger.info(f"Performing search with params: {search_params}")
        # TODO: Implement search logic

    @pyqtSlot()
    def clear_search(self):
        """Clear search and reload all data"""
        self.refresh_data()

    @pyqtSlot()
    def export_data(self):
        """Export shop data to Excel/PDF"""
        # TODO: Implement export functionality
        self.show_success("Export", "Fonctionnalité d'export en cours de développement")

    @pyqtSlot()
    def search_for_payment(self):
        """Search shops for payment initiation"""
        # TODO: Implement payment search
        pass

    @pyqtSlot(dict)
    def initiate_payment(self, shop_data: Dict[str, Any]):
        """Initiate payment for a shop"""
        # TODO: Implement payment initiation
        pass

    @pyqtSlot(dict)
    def validate_payment(self, payment_data: Dict[str, Any]):
        """Validate a pending payment"""
        # TODO: Implement payment validation
        pass

    @pyqtSlot(dict)
    def view_payment_details(self, payment_data: Dict[str, Any]):
        """View payment details"""
        # TODO: Implement payment details view
        pass

    @pyqtSlot(dict)
    def assign_owner(self, shop_data: Dict[str, Any]):
        """Assign owner to unoccupied shop"""
        # TODO: Implement owner assignment
        pass
        
        # Create shops tab
        shops_tab = QWidget()
        shops_layout = QVBoxLayout(shops_tab)
        
        # Create shops table
        self.shops_table = QTableWidget()
        self.shops_table.setColumnCount(12)
        self.shops_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Surface", "État", "Propriétaire", 
            "CIN/RC", "Adresse", "Téléphone", "Date d'occupation", 
            "Mois non payés", "Tarif", "Total"
        ])
        self.shops_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.shops_table.verticalHeader().setVisible(False)
        
        shops_layout.addWidget(self.shops_table)
        
        # Create payment tab
        payment_tab = QWidget()
        payment_layout = QVBoxLayout(payment_tab)
        
        # TODO: Add payment interface
        payment_layout.addWidget(QLabel("Interface de paiement à implémenter"))
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Propriétaire", "CIN/RC", 
            "Période", "Montant", "N° Créance", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(9)
        self.history_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Propriétaire", "CIN/RC", 
            "Période", "Mois", "Total", "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Create unused shops tab
        unused_tab = QWidget()
        unused_layout = QVBoxLayout(unused_tab)
        
        # Create unused shops table
        self.unused_table = QTableWidget()
        self.unused_table.setColumnCount(5)
        self.unused_table.setHorizontalHeaderLabels([
            "Secteur", "N° Boutique", "Surface", "État", "Actions"
        ])
        self.unused_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.unused_table.verticalHeader().setVisible(False)
        
        unused_layout.addWidget(self.unused_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(shops_tab, "Liste des Boutiques")
        self.tab_widget.addTab(payment_tab, "Initiation Paiement")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        self.tab_widget.addTab(unused_tab, "Boutiques Non Exploitées")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_shop(self):
        """Add a new shop"""
        try:
            # Créer directement la boîte de dialogue sans import
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QLabel, QLineEdit, QPushButton, QComboBox, QDoubleSpinBox, QCheckBox, QTextEdit, QDateEdit, QDialogButtonBox, QGroupBox
            from PyQt5.QtCore import Qt, QDate
            
            # Créer une boîte de dialogue simple
            dialog = QDialog(self)
            dialog.setWindowTitle("Ajouter une Boutique")
            dialog.setMinimumWidth(500)
            dialog.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
            dialog.setStyleSheet("QDialog { background-color: #f5f5f5; }")
            
            # Créer le layout principal
            main_layout = QVBoxLayout(dialog)
            
            # Créer le groupe d'informations de la boutique
            shop_group = QGroupBox("Informations de la Boutique")
            shop_layout = QFormLayout(shop_group)
            
            # Secteur
            sector_combo = QComboBox()
            sector_combo.addItems(["Secteur 1", "Secteur 2", "Secteur 3", "Secteur 4"])
            shop_layout.addRow("Secteur:", sector_combo)
            
            # Numéro de boutique
            number_edit = QLineEdit()
            shop_layout.addRow("N° Boutique:", number_edit)
            
            # Surface
            surface_spin = QDoubleSpinBox()
            surface_spin.setRange(1, 1000)
            surface_spin.setSuffix(" m²")
            shop_layout.addRow("Surface:", surface_spin)
            
            # État
            state_combo = QComboBox()
            state_combo.addItems(["Bon état", "Moyen", "À rénover"])
            shop_layout.addRow("État:", state_combo)
            
            # Tarif
            rate_spin = QDoubleSpinBox()
            rate_spin.setRange(1, 10000)
            rate_spin.setSuffix(" DH/mois")
            shop_layout.addRow("Tarif Mensuel:", rate_spin)
            
            main_layout.addWidget(shop_group)
            
            # Créer le groupe d'informations du locataire
            tenant_group = QGroupBox("Informations du Locataire")
            tenant_layout = QFormLayout(tenant_group)
            
            # Boutique louée
            is_rented_check = QCheckBox("Boutique Louée")
            tenant_layout.addRow("", is_rented_check)
            
            # Nom du locataire
            tenant_name_edit = QLineEdit()
            tenant_layout.addRow("Nom Complet:", tenant_name_edit)
            
            # CIN/RC
            tenant_id_edit = QLineEdit()
            tenant_layout.addRow("CIN/RC:", tenant_id_edit)
            
            # Téléphone
            tenant_phone_edit = QLineEdit()
            tenant_layout.addRow("Téléphone:", tenant_phone_edit)
            
            # Date d'occupation
            occupation_date_edit = QDateEdit()
            occupation_date_edit.setCalendarPopup(True)
            occupation_date_edit.setDate(QDate.currentDate())
            tenant_layout.addRow("Date d'Occupation:", occupation_date_edit)
            
            main_layout.addWidget(tenant_group)
            
            # Fonction pour activer/désactiver les champs du locataire
            def toggle_tenant_fields(state):
                enabled = bool(state)
                tenant_name_edit.setEnabled(enabled)
                tenant_id_edit.setEnabled(enabled)
                tenant_phone_edit.setEnabled(enabled)
                occupation_date_edit.setEnabled(enabled)
            
            # Connecter la case à cocher
            is_rented_check.stateChanged.connect(toggle_tenant_fields)
            
            # Désactiver les champs par défaut
            toggle_tenant_fields(False)
            
            # Créer les boutons
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            
            main_layout.addWidget(button_box)
            
            # Afficher la boîte de dialogue
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Récupérer les données
                data = {
                    "sector": sector_combo.currentText(),
                    "number": number_edit.text(),
                    "surface": surface_spin.value(),
                    "state": state_combo.currentText(),
                    "rate": rate_spin.value(),
                    "is_rented": is_rented_check.isChecked()
                }
                
                if data["is_rented"]:
                    data.update({
                        "tenant_name": tenant_name_edit.text(),
                        "tenant_id": tenant_id_edit.text(),
                        "tenant_phone": tenant_phone_edit.text(),
                        "occupation_date": occupation_date_edit.date().toString("yyyy-MM-dd")
                    })
                
                # Vérifier les données
                if not data["number"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir tous les champs obligatoires."
                    )
                    return
                
                if data["is_rented"] and not data["tenant_name"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir les informations du locataire."
                    )
                    return
                
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Boutique Ajoutée",
                    f"La boutique {data['number']} du {data['sector']} a été ajoutée avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )