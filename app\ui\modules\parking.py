#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Parking rights management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ParkingWidget(QWidget):
    """Parking rights management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize parking widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Gestion du Droit de Stationnement et de la Taxe sur le Transport Public")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add license button
        add_license_btn = QPushButton("Ajouter une Licence")
        add_license_btn.clicked.connect(self.add_license)
        header_layout.addWidget(add_license_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create licenses tab
        licenses_tab = QWidget()
        licenses_layout = QVBoxLayout(licenses_tab)
        
        # Create licenses table
        self.licenses_table = QTableWidget()
        self.licenses_table.setColumnCount(9)
        self.licenses_table.setHorizontalHeaderLabels([
            "N° Agrément", "Propriétaire", "CIN/RC", "Type", 
            "Immatriculation", "Adresse", "Téléphone", 
            "Dernier trimestre", "Total"
        ])
        self.licenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.licenses_table.verticalHeader().setVisible(False)
        
        licenses_layout.addWidget(self.licenses_table)
        
        # Create payment tab
        payment_tab = QWidget()
        payment_layout = QVBoxLayout(payment_tab)
        
        # TODO: Add payment interface
        payment_layout.addWidget(QLabel("Interface de paiement à implémenter"))
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "N° Agrément", "Propriétaire", "CIN/RC", "Type", 
            "Période", "Montant", "N° Créance", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(7)
        self.history_table.setHorizontalHeaderLabels([
            "N° Agrément", "Propriétaire", "CIN/RC", 
            "Période", "Total", "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(licenses_tab, "Liste des Licences")
        self.tab_widget.addTab(payment_tab, "Initiation Paiement")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_license(self):
        """Add a new license"""
        try:
            from app.ui.dialogs import ParkingDialog, ensure_app_imports
            
            # Ensure proper imports
            ensure_app_imports()
            
            dialog = ParkingDialog(self.db_manager, self)
            result = dialog.exec_()
            
            if result == ParkingDialog.Accepted:
                data = dialog.get_parking_data()
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Zone de Stationnement Ajoutée",
                    f"La zone de stationnement {data['name']} a été ajoutée avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )