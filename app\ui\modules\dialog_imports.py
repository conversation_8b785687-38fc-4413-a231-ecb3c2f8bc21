#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Import helper for dialog boxes
"""

import sys
import os

# Get the absolute path to the project root
current_dir = os.path.dirname(os.path.abspath(__file__))
ui_dir = os.path.dirname(current_dir)
app_dir = os.path.dirname(ui_dir)
project_root = os.path.dirname(app_dir)

# Add the project root to the Python path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import dialogs directly
try:
    sys.path.insert(0, app_dir)
    from ui.dialogs import (
        VehicleDialog, ShopDialog, OccupationDialog, SoukDialog,
        BeverageDialog, ParkingDialog, LandDialog
    )
except ImportError as e:
    print(f"Error importing dialogs: {e}")
    
    # Try another approach
    try:
        sys.path.insert(0, ui_dir)
        from dialogs import (
            VehicleDialog, ShopDialog, OccupationDialog, SoukDialog,
            BeverageDialog, ParkingDialog, LandDialog
        )
    except ImportError as e:
        print(f"Second attempt failed: {e}")