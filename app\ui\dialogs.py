#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dialog boxes for the Fiscal Resources Management Application
"""

# Helper function to ensure proper imports
def ensure_app_imports():
    """Ensure that the app module can be imported correctly"""
    import sys
    import os
    
    # Add the project root to the Python path
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    return True

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QMessageBox, QComboBox, QDateEdit,
    QFormLayout, QSpinBox, QDoubleSpinBox, QTextEdit, QCheckBox,
    QDialogButtonBox, QGroupBox
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class BaseDialog(QDialog):
    """Base dialog for data entry"""
    
    def __init__(self, parent=None, title="Formulaire"):
        """Initialize base dialog"""
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.setMinimumWidth(500)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(20)
        
        # Create form layout
        self.form_layout = QFormLayout()
        self.form_layout.setLabelAlignment(Qt.AlignRight)
        self.form_layout.setSpacing(15)
        
        # Create buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        
        # Add form layout to main layout
        self.main_layout.addLayout(self.form_layout)
        self.main_layout.addWidget(self.button_box)
        
        # Apply stylesheet
        self.apply_stylesheet()
    
    def apply_stylesheet(self):
        """Apply stylesheet to the dialog"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            
            QLabel {
                color: #2c3e50;
                font-size: 14px;
            }
            
            QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                background-color: #ffffff;
            }
            
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, 
            QSpinBox:focus, QDoubleSpinBox:focus, QTextEdit:focus {
                border: 1px solid #3498db;
            }
            
            QPushButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
            }
        """)

class VehicleDialog(BaseDialog):
    """Dialog for adding or editing a vehicle in the impound"""
    
    def __init__(self, db_manager, parent=None, vehicle_data=None):
        """Initialize vehicle dialog"""
        self.db_manager = db_manager
        self.vehicle_data = vehicle_data
        title = "Modifier un Véhicule" if vehicle_data else "Ajouter un Véhicule"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Vehicle type
        self.type_combo = QComboBox()
        self.load_vehicle_types()
        self.form_layout.addRow("Type de Véhicule:", self.type_combo)
        
        # License plate
        self.plate_edit = QLineEdit()
        self.plate_edit.setPlaceholderText("Ex: 12345-A-6")
        self.form_layout.addRow("Immatriculation:", self.plate_edit)
        
        # Brand and model
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("Ex: Renault")
        self.form_layout.addRow("Marque:", self.brand_edit)
        
        self.model_edit = QLineEdit()
        self.model_edit.setPlaceholderText("Ex: Clio")
        self.form_layout.addRow("Modèle:", self.model_edit)
        
        # Color
        self.color_edit = QLineEdit()
        self.color_edit.setPlaceholderText("Ex: Bleu")
        self.form_layout.addRow("Couleur:", self.color_edit)
        
        # Owner information
        owner_group = QGroupBox("Informations du Propriétaire")
        owner_layout = QFormLayout(owner_group)
        
        self.owner_name_edit = QLineEdit()
        owner_layout.addRow("Nom Complet:", self.owner_name_edit)
        
        self.owner_id_edit = QLineEdit()
        owner_layout.addRow("CIN:", self.owner_id_edit)
        
        self.owner_phone_edit = QLineEdit()
        owner_layout.addRow("Téléphone:", self.owner_phone_edit)
        
        self.owner_address_edit = QTextEdit()
        self.owner_address_edit.setMaximumHeight(80)
        owner_layout.addRow("Adresse:", self.owner_address_edit)
        
        self.main_layout.insertWidget(1, owner_group)
        
        # Impound information
        impound_group = QGroupBox("Informations de Mise en Fourrière")
        impound_layout = QFormLayout(impound_group)
        
        self.entry_date_edit = QDateEdit()
        self.entry_date_edit.setCalendarPopup(True)
        self.entry_date_edit.setDate(QDate.currentDate())
        impound_layout.addRow("Date d'Entrée:", self.entry_date_edit)
        
        self.reason_combo = QComboBox()
        self.reason_combo.addItems([
            "Stationnement Interdit", 
            "Stationnement Gênant",
            "Défaut de Documents",
            "Accident",
            "Abandon",
            "Autre"
        ])
        impound_layout.addRow("Motif:", self.reason_combo)
        
        self.location_edit = QLineEdit()
        impound_layout.addRow("Lieu de Saisie:", self.location_edit)
        
        self.agent_edit = QLineEdit()
        impound_layout.addRow("Agent Responsable:", self.agent_edit)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        impound_layout.addRow("Observations:", self.notes_edit)
        
        self.main_layout.insertWidget(2, impound_group)
        
        # Fill data if editing
        if self.vehicle_data:
            self.fill_vehicle_data()
    
    def load_vehicle_types(self):
        """Load vehicle types from database"""
        # TODO: Replace with actual database query
        self.type_combo.addItems([
            "Voiture", 
            "Camion",
            "Moto",
            "Vélo",
            "Autre"
        ])
    
    def fill_vehicle_data(self):
        """Fill form with existing vehicle data"""
        if not self.vehicle_data:
            return
            
        # TODO: Fill form fields with vehicle_data
        pass
    
    def get_vehicle_data(self):
        """Get vehicle data from form"""
        data = {
            "type": self.type_combo.currentText(),
            "plate": self.plate_edit.text(),
            "brand": self.brand_edit.text(),
            "model": self.model_edit.text(),
            "color": self.color_edit.text(),
            "owner_name": self.owner_name_edit.text(),
            "owner_id": self.owner_id_edit.text(),
            "owner_phone": self.owner_phone_edit.text(),
            "owner_address": self.owner_address_edit.toPlainText(),
            "entry_date": self.entry_date_edit.date().toString("yyyy-MM-dd"),
            "reason": self.reason_combo.currentText(),
            "location": self.location_edit.text(),
            "agent": self.agent_edit.text(),
            "notes": self.notes_edit.toPlainText()
        }
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.plate_edit.text() or not self.owner_name_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        # Get data
        data = self.get_vehicle_data()
        
        # TODO: Save to database
        
        super().accept()

class ShopDialog(BaseDialog):
    """Dialog for adding or editing a commercial shop"""
    
    def __init__(self, db_manager, parent=None, shop_data=None):
        """Initialize shop dialog"""
        self.db_manager = db_manager
        self.shop_data = shop_data
        title = "Modifier une Boutique" if shop_data else "Ajouter une Boutique"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Shop information
        shop_group = QGroupBox("Informations de la Boutique")
        shop_layout = QFormLayout(shop_group)
        
        self.sector_combo = QComboBox()
        self.sector_combo.addItems(["Secteur 1", "Secteur 2", "Secteur 3", "Secteur 4"])
        shop_layout.addRow("Secteur:", self.sector_combo)
        
        self.number_edit = QLineEdit()
        shop_layout.addRow("N° Boutique:", self.number_edit)
        
        self.surface_spin = QDoubleSpinBox()
        self.surface_spin.setRange(1, 1000)
        self.surface_spin.setSuffix(" m²")
        shop_layout.addRow("Surface:", self.surface_spin)
        
        self.state_combo = QComboBox()
        self.state_combo.addItems(["Bon état", "Moyen", "À rénover"])
        shop_layout.addRow("État:", self.state_combo)
        
        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setRange(1, 10000)
        self.rate_spin.setSuffix(" DH/mois")
        shop_layout.addRow("Tarif Mensuel:", self.rate_spin)
        
        self.main_layout.insertWidget(1, shop_group)
        
        # Tenant information
        tenant_group = QGroupBox("Informations du Locataire")
        tenant_layout = QFormLayout(tenant_group)
        
        self.is_rented_check = QCheckBox("Boutique Louée")
        self.is_rented_check.stateChanged.connect(self.toggle_tenant_fields)
        tenant_layout.addRow("", self.is_rented_check)
        
        self.tenant_name_edit = QLineEdit()
        tenant_layout.addRow("Nom Complet:", self.tenant_name_edit)
        
        self.tenant_id_edit = QLineEdit()
        tenant_layout.addRow("CIN/RC:", self.tenant_id_edit)
        
        self.tenant_phone_edit = QLineEdit()
        tenant_layout.addRow("Téléphone:", self.tenant_phone_edit)
        
        self.tenant_address_edit = QTextEdit()
        self.tenant_address_edit.setMaximumHeight(80)
        tenant_layout.addRow("Adresse:", self.tenant_address_edit)
        
        self.occupation_date_edit = QDateEdit()
        self.occupation_date_edit.setCalendarPopup(True)
        self.occupation_date_edit.setDate(QDate.currentDate())
        tenant_layout.addRow("Date d'Occupation:", self.occupation_date_edit)
        
        self.main_layout.insertWidget(2, tenant_group)
        
        # Fill data if editing
        if self.shop_data:
            self.fill_shop_data()
        else:
            self.toggle_tenant_fields(False)
    
    def toggle_tenant_fields(self, state):
        """Enable or disable tenant fields based on checkbox state"""
        enabled = bool(state)
        self.tenant_name_edit.setEnabled(enabled)
        self.tenant_id_edit.setEnabled(enabled)
        self.tenant_phone_edit.setEnabled(enabled)
        self.tenant_address_edit.setEnabled(enabled)
        self.occupation_date_edit.setEnabled(enabled)
    
    def fill_shop_data(self):
        """Fill form with existing shop data"""
        if not self.shop_data:
            return
            
        # TODO: Fill form fields with shop_data
        pass
    
    def get_shop_data(self):
        """Get shop data from form"""
        data = {
            "sector": self.sector_combo.currentText(),
            "number": self.number_edit.text(),
            "surface": self.surface_spin.value(),
            "state": self.state_combo.currentText(),
            "rate": self.rate_spin.value(),
            "is_rented": self.is_rented_check.isChecked()
        }
        
        if data["is_rented"]:
            data.update({
                "tenant_name": self.tenant_name_edit.text(),
                "tenant_id": self.tenant_id_edit.text(),
                "tenant_phone": self.tenant_phone_edit.text(),
                "tenant_address": self.tenant_address_edit.toPlainText(),
                "occupation_date": self.occupation_date_edit.date().toString("yyyy-MM-dd")
            })
        
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.number_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        if self.is_rented_check.isChecked() and not self.tenant_name_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir les informations du locataire."
            )
            return
        
        # Get data
        data = self.get_shop_data()
        
        # TODO: Save to database
        
        super().accept()

class OccupationDialog(BaseDialog):
    """Dialog for adding or editing a temporary occupation"""
    
    def __init__(self, db_manager, parent=None, occupation_data=None):
        """Initialize occupation dialog"""
        self.db_manager = db_manager
        self.occupation_data = occupation_data
        title = "Modifier une Occupation" if occupation_data else "Ajouter une Occupation"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Occupation information
        occupation_group = QGroupBox("Informations de l'Occupation")
        occupation_layout = QFormLayout(occupation_group)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "Terrasse de café", 
            "Étalage commercial",
            "Kiosque",
            "Chantier de construction",
            "Événement temporaire",
            "Autre"
        ])
        occupation_layout.addRow("Type d'Occupation:", self.type_combo)
        
        self.surface_spin = QDoubleSpinBox()
        self.surface_spin.setRange(0.1, 1000)
        self.surface_spin.setSuffix(" m²")
        occupation_layout.addRow("Surface:", self.surface_spin)
        
        self.location_edit = QLineEdit()
        occupation_layout.addRow("Adresse:", self.location_edit)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        occupation_layout.addRow("Date de Début:", self.start_date_edit)
        
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 120)
        self.duration_spin.setSuffix(" mois")
        occupation_layout.addRow("Durée:", self.duration_spin)
        
        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setRange(1, 1000)
        self.rate_spin.setSuffix(" DH/m²/trimestre")
        occupation_layout.addRow("Tarif:", self.rate_spin)
        
        self.main_layout.insertWidget(1, occupation_group)
        
        # Owner information
        owner_group = QGroupBox("Informations du Propriétaire")
        owner_layout = QFormLayout(owner_group)
        
        self.owner_name_edit = QLineEdit()
        owner_layout.addRow("Nom Complet:", self.owner_name_edit)
        
        self.owner_id_edit = QLineEdit()
        owner_layout.addRow("CIN/RC:", self.owner_id_edit)
        
        self.owner_phone_edit = QLineEdit()
        owner_layout.addRow("Téléphone:", self.owner_phone_edit)
        
        self.owner_address_edit = QTextEdit()
        self.owner_address_edit.setMaximumHeight(80)
        owner_layout.addRow("Adresse:", self.owner_address_edit)
        
        self.main_layout.insertWidget(2, owner_group)
        
        # Fill data if editing
        if self.occupation_data:
            self.fill_occupation_data()
    
    def fill_occupation_data(self):
        """Fill form with existing occupation data"""
        if not self.occupation_data:
            return
            
        # TODO: Fill form fields with occupation_data
        pass
    
    def get_occupation_data(self):
        """Get occupation data from form"""
        data = {
            "type": self.type_combo.currentText(),
            "surface": self.surface_spin.value(),
            "location": self.location_edit.text(),
            "start_date": self.start_date_edit.date().toString("yyyy-MM-dd"),
            "duration": self.duration_spin.value(),
            "rate": self.rate_spin.value(),
            "owner_name": self.owner_name_edit.text(),
            "owner_id": self.owner_id_edit.text(),
            "owner_phone": self.owner_phone_edit.text(),
            "owner_address": self.owner_address_edit.toPlainText()
        }
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.location_edit.text() or not self.owner_name_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        # Get data
        data = self.get_occupation_data()
        
        # TODO: Save to database
        
        super().accept()

class SoukDialog(BaseDialog):
    """Dialog for adding or editing a souk leasing"""
    
    def __init__(self, db_manager, parent=None, souk_data=None):
        """Initialize souk dialog"""
        self.db_manager = db_manager
        self.souk_data = souk_data
        title = "Modifier un Affermage" if souk_data else "Ajouter un Affermage"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Souk information
        souk_group = QGroupBox("Informations du Souk")
        souk_layout = QFormLayout(souk_group)
        
        self.name_edit = QLineEdit()
        souk_layout.addRow("Nom du Souk:", self.name_edit)
        
        self.location_edit = QLineEdit()
        souk_layout.addRow("Emplacement:", self.location_edit)
        
        self.day_combo = QComboBox()
        self.day_combo.addItems([
            "Lundi", "Mardi", "Mercredi", "Jeudi", 
            "Vendredi", "Samedi", "Dimanche"
        ])
        souk_layout.addRow("Jour:", self.day_combo)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "Souk Hebdomadaire", 
            "Marché Municipal",
            "Marché de Gros",
            "Marché aux Bestiaux",
            "Autre"
        ])
        souk_layout.addRow("Type:", self.type_combo)
        
        self.area_spin = QDoubleSpinBox()
        self.area_spin.setRange(100, 100000)
        self.area_spin.setSuffix(" m²")
        souk_layout.addRow("Superficie:", self.area_spin)
        
        self.main_layout.insertWidget(1, souk_group)
        
        # Leasing information
        leasing_group = QGroupBox("Informations d'Affermage")
        leasing_layout = QFormLayout(leasing_group)
        
        self.lessee_name_edit = QLineEdit()
        leasing_layout.addRow("Nom du Fermier:", self.lessee_name_edit)
        
        self.lessee_id_edit = QLineEdit()
        leasing_layout.addRow("CIN/RC:", self.lessee_id_edit)
        
        self.lessee_phone_edit = QLineEdit()
        leasing_layout.addRow("Téléphone:", self.lessee_phone_edit)
        
        self.lessee_address_edit = QTextEdit()
        self.lessee_address_edit.setMaximumHeight(80)
        leasing_layout.addRow("Adresse:", self.lessee_address_edit)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        leasing_layout.addRow("Date de Début:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate().addYears(1))
        leasing_layout.addRow("Date de Fin:", self.end_date_edit)
        
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(1000, 10000000)
        self.amount_spin.setSuffix(" DH")
        leasing_layout.addRow("Montant Annuel:", self.amount_spin)
        
        self.main_layout.insertWidget(2, leasing_group)
        
        # Fill data if editing
        if self.souk_data:
            self.fill_souk_data()
    
    def fill_souk_data(self):
        """Fill form with existing souk data"""
        if not self.souk_data:
            return
            
        # TODO: Fill form fields with souk_data
        pass
    
    def get_souk_data(self):
        """Get souk data from form"""
        data = {
            "name": self.name_edit.text(),
            "location": self.location_edit.text(),
            "day": self.day_combo.currentText(),
            "type": self.type_combo.currentText(),
            "area": self.area_spin.value(),
            "lessee_name": self.lessee_name_edit.text(),
            "lessee_id": self.lessee_id_edit.text(),
            "lessee_phone": self.lessee_phone_edit.text(),
            "lessee_address": self.lessee_address_edit.toPlainText(),
            "start_date": self.start_date_edit.date().toString("yyyy-MM-dd"),
            "end_date": self.end_date_edit.date().toString("yyyy-MM-dd"),
            "amount": self.amount_spin.value()
        }
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.name_edit.text() or not self.lessee_name_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        # Get data
        data = self.get_souk_data()
        
        # TODO: Save to database
        
        super().accept()

class BeverageDialog(BaseDialog):
    """Dialog for adding or editing a beverage establishment"""
    
    def __init__(self, db_manager, parent=None, beverage_data=None):
        """Initialize beverage dialog"""
        self.db_manager = db_manager
        self.beverage_data = beverage_data
        title = "Modifier un Établissement" if beverage_data else "Ajouter un Établissement"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Establishment information
        establishment_group = QGroupBox("Informations de l'Établissement")
        establishment_layout = QFormLayout(establishment_group)
        
        self.name_edit = QLineEdit()
        establishment_layout.addRow("Nom de l'Établissement:", self.name_edit)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "Café", 
            "Restaurant",
            "Bar",
            "Hôtel",
            "Autre"
        ])
        establishment_layout.addRow("Type:", self.type_combo)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "1ère Catégorie", 
            "2ème Catégorie",
            "3ème Catégorie"
        ])
        establishment_layout.addRow("Catégorie:", self.category_combo)
        
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        establishment_layout.addRow("Adresse:", self.address_edit)
        
        self.surface_spin = QDoubleSpinBox()
        self.surface_spin.setRange(10, 1000)
        self.surface_spin.setSuffix(" m²")
        establishment_layout.addRow("Surface:", self.surface_spin)
        
        self.main_layout.insertWidget(1, establishment_group)
        
        # Owner information
        owner_group = QGroupBox("Informations du Propriétaire")
        owner_layout = QFormLayout(owner_group)
        
        self.owner_name_edit = QLineEdit()
        owner_layout.addRow("Nom Complet:", self.owner_name_edit)
        
        self.owner_id_edit = QLineEdit()
        owner_layout.addRow("CIN/RC:", self.owner_id_edit)
        
        self.owner_phone_edit = QLineEdit()
        owner_layout.addRow("Téléphone:", self.owner_phone_edit)
        
        self.license_edit = QLineEdit()
        owner_layout.addRow("N° Licence:", self.license_edit)
        
        self.license_date_edit = QDateEdit()
        self.license_date_edit.setCalendarPopup(True)
        self.license_date_edit.setDate(QDate.currentDate())
        owner_layout.addRow("Date de Licence:", self.license_date_edit)
        
        self.main_layout.insertWidget(2, owner_group)
        
        # Fill data if editing
        if self.beverage_data:
            self.fill_beverage_data()
    
    def fill_beverage_data(self):
        """Fill form with existing beverage data"""
        if not self.beverage_data:
            return
            
        # TODO: Fill form fields with beverage_data
        pass
    
    def get_beverage_data(self):
        """Get beverage data from form"""
        data = {
            "name": self.name_edit.text(),
            "type": self.type_combo.currentText(),
            "category": self.category_combo.currentText(),
            "address": self.address_edit.toPlainText(),
            "surface": self.surface_spin.value(),
            "owner_name": self.owner_name_edit.text(),
            "owner_id": self.owner_id_edit.text(),
            "owner_phone": self.owner_phone_edit.text(),
            "license": self.license_edit.text(),
            "license_date": self.license_date_edit.date().toString("yyyy-MM-dd")
        }
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.name_edit.text() or not self.owner_name_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        # Get data
        data = self.get_beverage_data()
        
        # TODO: Save to database
        
        super().accept()

class ParkingDialog(BaseDialog):
    """Dialog for adding or editing a parking zone"""
    
    def __init__(self, db_manager, parent=None, parking_data=None):
        """Initialize parking dialog"""
        self.db_manager = db_manager
        self.parking_data = parking_data
        title = "Modifier une Zone" if parking_data else "Ajouter une Zone"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Parking zone information
        zone_group = QGroupBox("Informations de la Zone")
        zone_layout = QFormLayout(zone_group)
        
        self.name_edit = QLineEdit()
        zone_layout.addRow("Nom de la Zone:", self.name_edit)
        
        self.location_edit = QLineEdit()
        zone_layout.addRow("Emplacement:", self.location_edit)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "Zone Bleue", 
            "Zone Verte",
            "Zone Rouge",
            "Autre"
        ])
        zone_layout.addRow("Type:", self.type_combo)
        
        self.capacity_spin = QSpinBox()
        self.capacity_spin.setRange(1, 1000)
        self.capacity_spin.setSuffix(" places")
        zone_layout.addRow("Capacité:", self.capacity_spin)
        
        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setRange(1, 100)
        self.rate_spin.setSuffix(" DH/heure")
        zone_layout.addRow("Tarif Horaire:", self.rate_spin)
        
        self.daily_rate_spin = QDoubleSpinBox()
        self.daily_rate_spin.setRange(5, 500)
        self.daily_rate_spin.setSuffix(" DH/jour")
        zone_layout.addRow("Tarif Journalier:", self.daily_rate_spin)
        
        self.monthly_rate_spin = QDoubleSpinBox()
        self.monthly_rate_spin.setRange(100, 5000)
        self.monthly_rate_spin.setSuffix(" DH/mois")
        zone_layout.addRow("Tarif Mensuel:", self.monthly_rate_spin)
        
        self.main_layout.insertWidget(1, zone_group)
        
        # Management information
        management_group = QGroupBox("Informations de Gestion")
        management_layout = QFormLayout(management_group)
        
        self.manager_combo = QComboBox()
        self.manager_combo.addItems([
            "Gestion Directe", 
            "Concession",
            "Affermage"
        ])
        self.manager_combo.currentIndexChanged.connect(self.toggle_manager_fields)
        management_layout.addRow("Mode de Gestion:", self.manager_combo)
        
        self.company_edit = QLineEdit()
        management_layout.addRow("Société:", self.company_edit)
        
        self.contract_edit = QLineEdit()
        management_layout.addRow("N° Contrat:", self.contract_edit)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        management_layout.addRow("Date de Début:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate().addYears(1))
        management_layout.addRow("Date de Fin:", self.end_date_edit)
        
        self.main_layout.insertWidget(2, management_group)
        
        # Fill data if editing
        if self.parking_data:
            self.fill_parking_data()
        else:
            self.toggle_manager_fields(0)
    
    def toggle_manager_fields(self, index):
        """Enable or disable manager fields based on combo selection"""
        enabled = index > 0  # Enable if not "Gestion Directe"
        self.company_edit.setEnabled(enabled)
        self.contract_edit.setEnabled(enabled)
        self.start_date_edit.setEnabled(enabled)
        self.end_date_edit.setEnabled(enabled)
    
    def fill_parking_data(self):
        """Fill form with existing parking data"""
        if not self.parking_data:
            return
            
        # TODO: Fill form fields with parking_data
        pass
    
    def get_parking_data(self):
        """Get parking data from form"""
        data = {
            "name": self.name_edit.text(),
            "location": self.location_edit.text(),
            "type": self.type_combo.currentText(),
            "capacity": self.capacity_spin.value(),
            "rate": self.rate_spin.value(),
            "daily_rate": self.daily_rate_spin.value(),
            "monthly_rate": self.monthly_rate_spin.value(),
            "management": self.manager_combo.currentText()
        }
        
        if self.manager_combo.currentIndex() > 0:
            data.update({
                "company": self.company_edit.text(),
                "contract": self.contract_edit.text(),
                "start_date": self.start_date_edit.date().toString("yyyy-MM-dd"),
                "end_date": self.end_date_edit.date().toString("yyyy-MM-dd")
            })
        
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.name_edit.text() or not self.location_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        if self.manager_combo.currentIndex() > 0 and not self.company_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir les informations de la société."
            )
            return
        
        # Get data
        data = self.get_parking_data()
        
        # TODO: Save to database
        
        super().accept()

class LandDialog(BaseDialog):
    """Dialog for adding or editing an undeveloped land"""
    
    def __init__(self, db_manager, parent=None, land_data=None):
        """Initialize land dialog"""
        self.db_manager = db_manager
        self.land_data = land_data
        title = "Modifier un Terrain" if land_data else "Ajouter un Terrain"
        super().__init__(parent, title)
    
    def setup_ui(self):
        """Set up the user interface"""
        super().setup_ui()
        
        # Land information
        land_group = QGroupBox("Informations du Terrain")
        land_layout = QFormLayout(land_group)
        
        self.location_edit = QLineEdit()
        land_layout.addRow("Emplacement:", self.location_edit)
        
        self.lot_edit = QLineEdit()
        land_layout.addRow("N° Lot:", self.lot_edit)
        
        self.title_edit = QLineEdit()
        land_layout.addRow("Titre Foncier:", self.title_edit)
        
        self.surface_spin = QDoubleSpinBox()
        self.surface_spin.setRange(1, 100000)
        self.surface_spin.setSuffix(" m²")
        land_layout.addRow("Surface:", self.surface_spin)
        
        self.zone_combo = QComboBox()
        self.load_zones()
        land_layout.addRow("Zone:", self.zone_combo)
        
        self.main_layout.insertWidget(1, land_group)
        
        # Owner information
        owner_group = QGroupBox("Informations du Propriétaire")
        owner_layout = QFormLayout(owner_group)
        
        self.owner_name_edit = QLineEdit()
        owner_layout.addRow("Nom Complet:", self.owner_name_edit)
        
        self.owner_id_edit = QLineEdit()
        owner_layout.addRow("CIN/RC:", self.owner_id_edit)
        
        self.owner_phone_edit = QLineEdit()
        owner_layout.addRow("Téléphone:", self.owner_phone_edit)
        
        self.owner_address_edit = QTextEdit()
        self.owner_address_edit.setMaximumHeight(80)
        owner_layout.addRow("Adresse:", self.owner_address_edit)
        
        self.acquisition_date_edit = QDateEdit()
        self.acquisition_date_edit.setCalendarPopup(True)
        self.acquisition_date_edit.setDate(QDate.currentDate())
        owner_layout.addRow("Date d'Acquisition:", self.acquisition_date_edit)
        
        self.main_layout.insertWidget(2, owner_group)
        
        # Fill data if editing
        if self.land_data:
            self.fill_land_data()
    
    def load_zones(self):
        """Load zones from database"""
        # TODO: Replace with actual database query
        self.zone_combo.addItems([
            "Zone 1", 
            "Zone 2",
            "Zone 3",
            "Zone 4"
        ])
    
    def fill_land_data(self):
        """Fill form with existing land data"""
        if not self.land_data:
            return
            
        # TODO: Fill form fields with land_data
        pass
    
    def get_land_data(self):
        """Get land data from form"""
        data = {
            "location": self.location_edit.text(),
            "lot": self.lot_edit.text(),
            "title": self.title_edit.text(),
            "surface": self.surface_spin.value(),
            "zone": self.zone_combo.currentText(),
            "owner_name": self.owner_name_edit.text(),
            "owner_id": self.owner_id_edit.text(),
            "owner_phone": self.owner_phone_edit.text(),
            "owner_address": self.owner_address_edit.toPlainText(),
            "acquisition_date": self.acquisition_date_edit.date().toString("yyyy-MM-dd")
        }
        return data
    
    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self.location_edit.text() or not self.owner_name_edit.text():
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs obligatoires."
            )
            return
        
        # Get data
        data = self.get_land_data()
        
        # TODO: Save to database
        
        super().accept()