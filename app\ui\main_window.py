#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main window for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget,
    QPushButton, QLabel, QFrame, QSizePolicy, QSpacerItem, QMessageBox,
    QToolBar, QAction, QStatusBar, QProgressBar, QSystemTrayIcon, QMenu,
    QSplitter, QScrollArea, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QSize, QTimer, QPropertyAnimation, QEasingCurve, pyqtSlot
from PyQt5.QtGui import QIcon, QFont, QPixmap, QPainter, QColor

from ui.login_dialog import LoginDialog
from ui.dashboard import DashboardWidget
from ui.admin_panel import AdminPanelWidget
from ui.modules.impound import ImpoundWidget
from ui.modules.shops import ShopsWidget
from ui.modules.souks import SouksWidget
from ui.modules.beverages import BeveragesWidget
from ui.modules.parking import ParkingWidget
from ui.modules.occupation import OccupationWidget
from ui.modules.lands import LandsWidget
from ui.theme_manager import get_theme_manager, get_current_colors

from utils.auth import Auth
from utils.logging_config import get_logger

class MainWindow(QMainWindow):
    """Enhanced main application window with modern UI"""

    def __init__(self, db_manager, config):
        """Initialize main window"""
        super().__init__()

        self.db_manager = db_manager
        self.config = config
        self.auth = Auth(db_manager)
        self.logger = get_logger("main_window")
        self.theme_manager = get_theme_manager()

        # Window properties
        self.setWindowTitle("Gestion des Ressources Fiscales Communales")
        self.setMinimumSize(1024, 768)
        self.resize(
            self.config.get_setting("window_size.width", 1400),
            self.config.get_setting("window_size.height", 900)
        )

        # Modern window properties
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint |
                           Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

        # Animation properties
        self.sidebar_collapsed = False
        self.sidebar_animation = None

        # Status and progress tracking
        self.loading_operations = 0

        self.setup_ui()
        self.setup_theme()
        self.setup_system_tray()
        self.show_login()
    
    def setup_ui(self):
        """Set up the modern user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create modern sidebar with shadow effect
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setMinimumWidth(280)
        self.sidebar.setMaximumWidth(280)
        self.sidebar.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)

        # Add shadow effect to sidebar
        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(20)
        shadow_effect.setColor(QColor(0, 0, 0, 60))
        shadow_effect.setOffset(2, 0)
        self.sidebar.setGraphicsEffect(shadow_effect)
        
        # Create sidebar layout
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # Create app title
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_frame.setMinimumHeight(60)
        title_frame.setMaximumHeight(60)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 0, 0, 0)
        
        title_label = QLabel("GestionFiscale")
        title_label.setObjectName("titleLabel")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        title_layout.addWidget(title_label)
        sidebar_layout.addWidget(title_frame)
        
        # Create menu buttons
        self.menu_frame = QFrame()
        self.menu_frame.setObjectName("menuFrame")
        
        menu_layout = QVBoxLayout(self.menu_frame)
        menu_layout.setContentsMargins(0, 10, 0, 10)
        menu_layout.setSpacing(5)
        
        # Dashboard button
        self.dashboard_btn = self.create_menu_button("Tableau de Bord", "dashboard")
        menu_layout.addWidget(self.dashboard_btn)
        
        # Impound management button
        self.impound_btn = self.create_menu_button("Droits de Fourrière", "impound")
        menu_layout.addWidget(self.impound_btn)
        
        # Commercial shops button
        self.shops_btn = self.create_menu_button("Locaux Commerciaux", "shops")
        menu_layout.addWidget(self.shops_btn)
        
        # Souk leasing button
        self.souks_btn = self.create_menu_button("Affermage des Souks", "souks")
        menu_layout.addWidget(self.souks_btn)
        
        # Beverage establishments button
        self.beverages_btn = self.create_menu_button("Débits de Boissons", "beverages")
        menu_layout.addWidget(self.beverages_btn)
        
        # Parking rights button
        self.parking_btn = self.create_menu_button("Droit de Stationnement", "parking")
        menu_layout.addWidget(self.parking_btn)
        
        # Temporary occupation button
        self.occupation_btn = self.create_menu_button("Occupation Temporaire", "occupation")
        menu_layout.addWidget(self.occupation_btn)
        
        # Undeveloped lands button
        self.lands_btn = self.create_menu_button("Terrains Non Bâtis", "lands")
        menu_layout.addWidget(self.lands_btn)
        
        # Admin panel button
        self.admin_btn = self.create_menu_button("Administration", "admin")
        menu_layout.addWidget(self.admin_btn)
        
        # Add spacer
        menu_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Language switcher
        language_frame = QFrame()
        language_layout = QHBoxLayout(language_frame)
        language_layout.setContentsMargins(20, 10, 20, 10)
        
        self.fr_btn = QPushButton("FR")
        self.fr_btn.setObjectName("languageButton")
        self.fr_btn.setCheckable(True)
        self.fr_btn.setChecked(self.config.get_setting("language") == "fr")
        self.fr_btn.clicked.connect(lambda: self.switch_language("fr"))
        
        self.ar_btn = QPushButton("AR")
        self.ar_btn.setObjectName("languageButton")
        self.ar_btn.setCheckable(True)
        self.ar_btn.setChecked(self.config.get_setting("language") == "ar")
        self.ar_btn.clicked.connect(lambda: self.switch_language("ar"))
        
        language_layout.addWidget(self.fr_btn)
        language_layout.addWidget(self.ar_btn)
        
        menu_layout.addWidget(language_frame)
        
        # User info and logout
        self.user_frame = QFrame()
        self.user_frame.setObjectName("userFrame")
        self.user_frame.setMinimumHeight(80)
        self.user_frame.setMaximumHeight(80)
        
        user_layout = QVBoxLayout(self.user_frame)
        user_layout.setContentsMargins(20, 10, 20, 20)
        
        self.user_label = QLabel("Non connecté")
        self.user_label.setObjectName("userLabel")
        
        self.logout_btn = QPushButton("Déconnexion")
        self.logout_btn.setObjectName("logoutButton")
        self.logout_btn.clicked.connect(self.logout)
        
        user_layout.addWidget(self.user_label)
        user_layout.addWidget(self.logout_btn)
        
        menu_layout.addWidget(self.user_frame)
        
        sidebar_layout.addWidget(self.menu_frame)
        
        # Add sidebar to main layout
        main_layout.addWidget(self.sidebar)
        
        # Create content area
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("contentArea")
        
        # Create and add widgets to content area
        self.dashboard_widget = DashboardWidget(self.db_manager, self.auth)
        self.admin_panel_widget = AdminPanelWidget(self.db_manager, self.auth)
        self.impound_widget = ImpoundWidget(self.db_manager, self.auth)
        self.shops_widget = ShopsWidget(self.db_manager, self.auth)
        self.souks_widget = SouksWidget(self.db_manager, self.auth)
        self.beverages_widget = BeveragesWidget(self.db_manager, self.auth)
        self.parking_widget = ParkingWidget(self.db_manager, self.auth)
        self.occupation_widget = OccupationWidget(self.db_manager, self.auth)
        self.lands_widget = LandsWidget(self.db_manager, self.auth)
        
        self.content_area.addWidget(self.dashboard_widget)
        self.content_area.addWidget(self.admin_panel_widget)
        self.content_area.addWidget(self.impound_widget)
        self.content_area.addWidget(self.shops_widget)
        self.content_area.addWidget(self.souks_widget)
        self.content_area.addWidget(self.beverages_widget)
        self.content_area.addWidget(self.parking_widget)
        self.content_area.addWidget(self.occupation_widget)
        self.content_area.addWidget(self.lands_widget)
        
        # Add content area to main layout
        main_layout.addWidget(self.content_area)
        
        # Connect menu buttons
        self.dashboard_btn.clicked.connect(lambda: self.switch_page(0))
        self.admin_btn.clicked.connect(lambda: self.switch_page(1))
        self.impound_btn.clicked.connect(lambda: self.switch_page(2))
        self.shops_btn.clicked.connect(lambda: self.switch_page(3))
        self.souks_btn.clicked.connect(lambda: self.switch_page(4))
        self.beverages_btn.clicked.connect(lambda: self.switch_page(5))
        self.parking_btn.clicked.connect(lambda: self.switch_page(6))
        self.occupation_btn.clicked.connect(lambda: self.switch_page(7))
        self.lands_btn.clicked.connect(lambda: self.switch_page(8))
        
        # Set initial page
        self.content_area.setCurrentIndex(0)

        # Setup modern toolbar
        self.setup_toolbar()

        # Setup modern status bar
        self.setup_status_bar()

        # Apply modern stylesheet
        self.apply_modern_stylesheet()
    
    def create_menu_button(self, text, object_name):
        """Create a menu button for the sidebar"""
        button = QPushButton(text)
        button.setObjectName(f"{object_name}Button")
        button.setMinimumHeight(50)
        button.setIconSize(QSize(24, 24))
        button.setCheckable(True)
        
        return button

    def setup_toolbar(self):
        """Setup modern toolbar with actions"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setObjectName("mainToolbar")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)

        # Sidebar toggle action
        toggle_action = QAction("☰", self)
        toggle_action.setToolTip("Réduire/Étendre le menu")
        toggle_action.triggered.connect(self.toggle_sidebar)
        toolbar.addAction(toggle_action)

        toolbar.addSeparator()

        # Refresh action
        refresh_action = QAction("🔄", self)
        refresh_action.setToolTip("Actualiser les données")
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_current_page)
        toolbar.addAction(refresh_action)

        # Search action
        search_action = QAction("🔍", self)
        search_action.setToolTip("Recherche globale")
        search_action.setShortcut("Ctrl+F")
        search_action.triggered.connect(self.show_global_search)
        toolbar.addAction(search_action)

        toolbar.addSeparator()

        # Theme toggle action
        theme_action = QAction("🌙", self)
        theme_action.setToolTip("Changer le thème")
        theme_action.triggered.connect(self.toggle_theme)
        toolbar.addAction(theme_action)

        # Settings action
        settings_action = QAction("⚙️", self)
        settings_action.setToolTip("Paramètres")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)

        self.addToolBar(toolbar)

    def setup_status_bar(self):
        """Setup modern status bar with information"""
        status_bar = QStatusBar()
        status_bar.setObjectName("modernStatusBar")

        # User info label
        self.status_user_label = QLabel("Non connecté")
        self.status_user_label.setObjectName("statusUserLabel")
        status_bar.addWidget(self.status_user_label)

        # Connection status
        self.status_connection_label = QLabel("🔴 Déconnecté")
        self.status_connection_label.setObjectName("statusConnectionLabel")
        status_bar.addPermanentWidget(self.status_connection_label)

        # Progress bar for operations
        self.status_progress = QProgressBar()
        self.status_progress.setObjectName("statusProgress")
        self.status_progress.setVisible(False)
        self.status_progress.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.status_progress)

        # Current time
        self.status_time_label = QLabel()
        self.status_time_label.setObjectName("statusTimeLabel")
        status_bar.addPermanentWidget(self.status_time_label)

        # Update time every second
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        self.update_time()

        self.setStatusBar(status_bar)

    def setup_theme(self):
        """Setup theme management"""
        # Initialize theme manager
        self.theme_manager.initialize_theme()

        # Connect theme change signal
        self.theme_manager.theme_changed.connect(self.on_theme_changed)

    def setup_system_tray(self):
        """Setup system tray icon"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)

            # Create tray menu
            tray_menu = QMenu()

            show_action = tray_menu.addAction("Afficher")
            show_action.triggered.connect(self.show)

            hide_action = tray_menu.addAction("Masquer")
            hide_action.triggered.connect(self.hide)

            tray_menu.addSeparator()

            quit_action = tray_menu.addAction("Quitter")
            quit_action.triggered.connect(self.close)

            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.activated.connect(self.tray_icon_activated)

            # Set tray icon (you would set an actual icon here)
            # self.tray_icon.setIcon(QIcon("path/to/icon.png"))
            self.tray_icon.show()

    @pyqtSlot()
    def toggle_sidebar(self):
        """Toggle sidebar visibility with animation"""
        if not self.sidebar_animation:
            self.sidebar_animation = QPropertyAnimation(self.sidebar, b"minimumWidth")
            self.sidebar_animation.setDuration(300)
            self.sidebar_animation.setEasingCurve(QEasingCurve.InOutQuart)

        if self.sidebar_collapsed:
            # Expand sidebar
            self.sidebar_animation.setStartValue(60)
            self.sidebar_animation.setEndValue(280)
            self.sidebar_collapsed = False
        else:
            # Collapse sidebar
            self.sidebar_animation.setStartValue(280)
            self.sidebar_animation.setEndValue(60)
            self.sidebar_collapsed = True

        self.sidebar_animation.start()

    @pyqtSlot()
    def refresh_current_page(self):
        """Refresh current page data"""
        current_widget = self.content_area.currentWidget()
        if hasattr(current_widget, 'refresh_data'):
            self.show_loading("Actualisation des données...")
            current_widget.refresh_data()
            self.hide_loading()

    @pyqtSlot()
    def show_global_search(self):
        """Show global search dialog"""
        # TODO: Implement global search
        QMessageBox.information(self, "Recherche", "Fonctionnalité de recherche globale en cours de développement")

    @pyqtSlot()
    def toggle_theme(self):
        """Toggle between light and dark theme"""
        current_theme = self.theme_manager.get_current_theme()
        new_theme = "dark" if current_theme == "light" else "light"
        self.theme_manager.set_theme(new_theme)

    @pyqtSlot()
    def show_settings(self):
        """Show settings dialog"""
        # TODO: Implement settings dialog
        QMessageBox.information(self, "Paramètres", "Dialogue des paramètres en cours de développement")

    def show_loading(self, message="Chargement..."):
        """Show loading indicator"""
        self.loading_operations += 1
        self.status_progress.setVisible(True)
        self.status_progress.setRange(0, 0)  # Indeterminate progress
        self.statusBar().showMessage(message)

    def hide_loading(self, message="Prêt"):
        """Hide loading indicator"""
        self.loading_operations = max(0, self.loading_operations - 1)
        if self.loading_operations == 0:
            self.status_progress.setVisible(False)
            self.statusBar().showMessage(message)

    def update_time(self):
        """Update time display in status bar"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.status_time_label.setText(current_time)

    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.DoubleClick:
            if self.isVisible():
                self.hide()
            else:
                self.show()
                self.raise_()
                self.activateWindow()

    @pyqtSlot(str)
    def on_theme_changed(self, theme_name):
        """Handle theme change"""
        self.logger.info(f"Theme changed to: {theme_name}")
        # Update any theme-dependent UI elements
        self.apply_modern_stylesheet()

    def apply_modern_stylesheet(self):
        """Apply modern stylesheet with current theme colors"""
        colors = get_current_colors()

        # Get primary colors or use defaults
        primary = colors.get('primary', '#1976d2')
        surface = colors.get('surface', '#ffffff')
        background = colors.get('background', '#fafafa')
        text_primary = colors.get('text_primary', '#212121')
        text_secondary = colors.get('text_secondary', '#757575')

        # Modern stylesheet with dynamic colors
        modern_style = f"""
            QMainWindow {{
                background-color: {background};
                color: {text_primary};
            }}

            #sidebar {{
                background-color: {primary};
                color: white;
                border: none;
            }}

            #titleFrame {{
                background-color: rgba(0, 0, 0, 0.1);
                border: none;
            }}

            #titleLabel {{
                color: white;
                font-size: 16px;
                font-weight: 500;
            }}

            #menuFrame QPushButton {{
                background-color: transparent;
                color: white;
                border: none;
                text-align: left;
                padding: 16px 24px;
                font-size: 14px;
                font-weight: 400;
                border-radius: 0px;
            }}

            #menuFrame QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.1);
            }}

            #menuFrame QPushButton:checked {{
                background-color: rgba(255, 255, 255, 0.2);
                border-left: 4px solid white;
                font-weight: 500;
            }}

            #userFrame {{
                background-color: rgba(0, 0, 0, 0.1);
                border: none;
                padding: 16px;
            }}

            #userLabel {{
                color: white;
                font-size: 14px;
                font-weight: 500;
            }}

            #logoutButton {{
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 500;
            }}

            #logoutButton:hover {{
                background-color: #d32f2f;
            }}

            #languageButton {{
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: 500;
                margin: 2px;
            }}

            #languageButton:checked {{
                background-color: white;
                color: {primary};
            }}

            #contentArea {{
                background-color: {surface};
                border: none;
            }}

            #mainToolbar {{
                background-color: {surface};
                border: none;
                border-bottom: 1px solid #e0e0e0;
                padding: 8px;
                spacing: 8px;
            }}

            #mainToolbar QToolButton {{
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-size: 16px;
            }}

            #mainToolbar QToolButton:hover {{
                background-color: rgba(25, 118, 210, 0.04);
            }}

            #modernStatusBar {{
                background-color: {surface};
                border-top: 1px solid #e0e0e0;
                color: {text_secondary};
                font-size: 12px;
            }}

            #statusUserLabel {{
                color: {text_primary};
                font-weight: 500;
            }}

            #statusConnectionLabel {{
                color: {text_secondary};
            }}

            #statusTimeLabel {{
                color: {text_secondary};
                font-family: monospace;
            }}

            #statusProgress {{
                border: none;
                border-radius: 4px;
                background-color: #e0e0e0;
            }}

            #statusProgress::chunk {{
                background-color: {primary};
                border-radius: 4px;
            }}
        """

        self.setStyleSheet(modern_style)
    
    def switch_page(self, index):
        """Switch to a different page in the content area"""
        if not self.auth.is_authenticated():
            self.show_login()
            return
        
        # Uncheck all buttons
        for button in self.menu_frame.findChildren(QPushButton):
            if button.objectName().endswith("Button"):
                button.setChecked(False)
        
        # Check the active button
        buttons = [
            self.dashboard_btn, self.admin_btn, self.impound_btn,
            self.shops_btn, self.souks_btn, self.beverages_btn,
            self.parking_btn, self.occupation_btn, self.lands_btn
        ]
        
        if 0 <= index < len(buttons):
            buttons[index].setChecked(True)
        
        # Switch page
        self.content_area.setCurrentIndex(index)
    
    def switch_language(self, language):
        """Switch application language"""
        if language == "fr":
            self.fr_btn.setChecked(True)
            self.ar_btn.setChecked(False)
        else:
            self.fr_btn.setChecked(False)
            self.ar_btn.setChecked(True)
        
        self.config.set_setting("language", language)
        
        # Show message to restart application
        QMessageBox.information(
            self,
            "Changement de Langue",
            "Veuillez redémarrer l'application pour appliquer le changement de langue."
        )
    
    def show_login(self):
        """Show login dialog"""
        login_dialog = LoginDialog(self.auth)
        result = login_dialog.exec_()
        
        if result == LoginDialog.Accepted:
            self.update_user_info()
            self.switch_page(0)  # Show dashboard
        else:
            # Exit application if login canceled
            self.close()
    
    def logout(self):
        """Log out the current user"""
        self.auth.logout()
        self.show_login()
    
    def update_user_info(self):
        """Update user information in the UI"""
        user = self.auth.get_current_user()
        if user:
            self.user_label.setText(f"{user['full_name']} ({user['role']})")
            self.status_user_label.setText(f"👤 {user['full_name']}")
            self.status_connection_label.setText("🟢 Connecté")

            # Show/hide admin button based on permissions
            self.admin_btn.setVisible(self.auth.has_permission("manage_users"))

            self.logger.info(f"User logged in: {user['username']}")
        else:
            self.user_label.setText("Non connecté")
            self.status_user_label.setText("Non connecté")
            self.status_connection_label.setText("🔴 Déconnecté")
    
    def show_notification(self, title, message, icon_type="info"):
        """Show system notification"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            if icon_type == "error":
                icon = QSystemTrayIcon.Critical
            elif icon_type == "warning":
                icon = QSystemTrayIcon.Warning
            else:
                icon = QSystemTrayIcon.Information

            self.tray_icon.showMessage(title, message, icon, 3000)

    def get_responsive_size_class(self):
        """Get current responsive size class"""
        width = self.width()
        return self.theme_manager.get_responsive_size_class(width)

    def resizeEvent(self, event):
        """Handle window resize for responsive design"""
        super().resizeEvent(event)

        # Adjust UI based on window size
        size_class = self.get_responsive_size_class()

        if size_class == "small":
            # Collapse sidebar on small screens
            if not self.sidebar_collapsed:
                self.toggle_sidebar()
        elif size_class in ["large", "extra_large"]:
            # Expand sidebar on large screens
            if self.sidebar_collapsed:
                self.toggle_sidebar()
    
    def closeEvent(self, event):
        """Handle window close event with modern features"""
        # Save window state
        self.config.set_setting("window_size.width", self.width())
        self.config.set_setting("window_size.height", self.height())
        self.config.set_setting("window_maximized", self.isMaximized())
        self.config.set_setting("sidebar_collapsed", self.sidebar_collapsed)

        # Stop timers
        if hasattr(self, 'time_timer'):
            self.time_timer.stop()

        # Hide system tray icon
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()

        # Close database connection
        self.db_manager.disconnect()

        # Log application close
        self.logger.info("Application closing")

        event.accept()