#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Theme Manager for the Fiscal Resources Management Application
Handles modern themes, responsive design, and accessibility features
"""

import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal, QSettings
from PyQt5.QtGui import QFont, QPalette, QColor
from typing import Dict, Any, Optional
from utils.logging_config import get_logger


class ThemeManager(QObject):
    """Modern theme manager with responsive design support"""
    
    # Signals
    theme_changed = pyqtSignal(str)
    font_size_changed = pyqtSignal(int)
    
    def __init__(self):
        """Initialize theme manager"""
        super().__init__()
        
        self.logger = get_logger("theme")
        self.settings = QSettings("FiscalApp", "Theme")
        
        # Theme definitions
        self.themes = {
            'light': {
                'name': 'Light Theme',
                'description': 'Modern light theme with Material Design',
                'stylesheet': 'style.qss',
                'colors': {
                    'primary': '#1976d2',
                    'primary_dark': '#1565c0',
                    'primary_light': '#42a5f5',
                    'secondary': '#f57c00',
                    'surface': '#ffffff',
                    'background': '#fafafa',
                    'error': '#d32f2f',
                    'warning': '#f57c00',
                    'success': '#388e3c',
                    'info': '#1976d2',
                    'text_primary': '#212121',
                    'text_secondary': '#757575',
                    'text_disabled': '#bdbdbd',
                    'divider': '#e0e0e0'
                }
            },
            'dark': {
                'name': 'Dark Theme',
                'description': 'Modern dark theme for low-light environments',
                'stylesheet': 'dark_style.qss',
                'colors': {
                    'primary': '#2196f3',
                    'primary_dark': '#1976d2',
                    'primary_light': '#64b5f6',
                    'secondary': '#ff9800',
                    'surface': '#424242',
                    'background': '#303030',
                    'error': '#f44336',
                    'warning': '#ff9800',
                    'success': '#4caf50',
                    'info': '#2196f3',
                    'text_primary': '#ffffff',
                    'text_secondary': '#b0b0b0',
                    'text_disabled': '#757575',
                    'divider': '#616161'
                }
            },
            'high_contrast': {
                'name': 'High Contrast',
                'description': 'High contrast theme for accessibility',
                'stylesheet': 'high_contrast_style.qss',
                'colors': {
                    'primary': '#0066cc',
                    'primary_dark': '#004499',
                    'primary_light': '#3388ff',
                    'secondary': '#ff6600',
                    'surface': '#ffffff',
                    'background': '#ffffff',
                    'error': '#cc0000',
                    'warning': '#ff6600',
                    'success': '#009900',
                    'info': '#0066cc',
                    'text_primary': '#000000',
                    'text_secondary': '#333333',
                    'text_disabled': '#666666',
                    'divider': '#000000'
                }
            }
        }
        
        # Font size options
        self.font_sizes = {
            'small': 12,
            'normal': 14,
            'large': 16,
            'extra_large': 18
        }
        
        # Current settings
        self.current_theme = self.settings.value('current_theme', 'light')
        self.current_font_size = self.settings.value('font_size', 'normal')
        self.high_contrast_mode = self.settings.value('high_contrast', False, type=bool)
        self.rtl_mode = self.settings.value('rtl_mode', False, type=bool)
        
        # Screen size breakpoints for responsive design
        self.breakpoints = {
            'small': 1024,
            'medium': 1366,
            'large': 1920,
            'extra_large': 2560
        }
    
    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
        """Get all available themes"""
        return self.themes
    
    def get_current_theme(self) -> str:
        """Get current theme name"""
        return self.current_theme
    
    def get_theme_colors(self, theme_name: Optional[str] = None) -> Dict[str, str]:
        """Get colors for specified theme or current theme"""
        theme_name = theme_name or self.current_theme
        return self.themes.get(theme_name, {}).get('colors', {})
    
    def set_theme(self, theme_name: str) -> bool:
        """Set application theme"""
        if theme_name not in self.themes:
            self.logger.warning(f"Theme '{theme_name}' not found")
            return False
        
        try:
            # Load stylesheet
            stylesheet_path = os.path.join(
                os.path.dirname(__file__), 
                'resources', 
                self.themes[theme_name]['stylesheet']
            )
            
            if os.path.exists(stylesheet_path):
                with open(stylesheet_path, 'r', encoding='utf-8') as f:
                    stylesheet = f.read()
            else:
                # Fallback to default stylesheet
                default_path = os.path.join(
                    os.path.dirname(__file__), 
                    'resources', 
                    'style.qss'
                )
                with open(default_path, 'r', encoding='utf-8') as f:
                    stylesheet = f.read()
            
            # Apply theme colors to stylesheet
            colors = self.get_theme_colors(theme_name)
            for color_name, color_value in colors.items():
                stylesheet = stylesheet.replace(f'var(--{color_name})', color_value)
            
            # Apply stylesheet to application
            app = QApplication.instance()
            if app:
                app.setStyleSheet(stylesheet)
            
            # Update current theme
            self.current_theme = theme_name
            self.settings.setValue('current_theme', theme_name)
            
            # Emit signal
            self.theme_changed.emit(theme_name)
            
            self.logger.info(f"Theme changed to: {theme_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting theme '{theme_name}': {e}")
            return False
    
    def set_font_size(self, size_name: str) -> bool:
        """Set application font size"""
        if size_name not in self.font_sizes:
            self.logger.warning(f"Font size '{size_name}' not found")
            return False
        
        try:
            font_size = self.font_sizes[size_name]
            
            # Update application font
            app = QApplication.instance()
            if app:
                font = app.font()
                font.setPointSize(font_size)
                app.setFont(font)
            
            # Update current font size
            self.current_font_size = size_name
            self.settings.setValue('font_size', size_name)
            
            # Emit signal
            self.font_size_changed.emit(font_size)
            
            self.logger.info(f"Font size changed to: {size_name} ({font_size}px)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting font size '{size_name}': {e}")
            return False
    
    def toggle_high_contrast(self) -> bool:
        """Toggle high contrast mode"""
        self.high_contrast_mode = not self.high_contrast_mode
        self.settings.setValue('high_contrast', self.high_contrast_mode)
        
        if self.high_contrast_mode:
            return self.set_theme('high_contrast')
        else:
            # Return to previous theme
            previous_theme = self.settings.value('previous_theme', 'light')
            return self.set_theme(previous_theme)
    
    def set_rtl_mode(self, enabled: bool) -> bool:
        """Set right-to-left text direction"""
        try:
            app = QApplication.instance()
            if app:
                if enabled:
                    app.setLayoutDirection(1)  # Qt.RightToLeft
                else:
                    app.setLayoutDirection(0)  # Qt.LeftToRight
            
            self.rtl_mode = enabled
            self.settings.setValue('rtl_mode', enabled)
            
            self.logger.info(f"RTL mode {'enabled' if enabled else 'disabled'}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting RTL mode: {e}")
            return False
    
    def get_responsive_size_class(self, width: int) -> str:
        """Get responsive size class based on screen width"""
        if width <= self.breakpoints['small']:
            return 'small'
        elif width <= self.breakpoints['medium']:
            return 'medium'
        elif width <= self.breakpoints['large']:
            return 'large'
        else:
            return 'extra_large'
    
    def apply_responsive_styles(self, widget, size_class: str):
        """Apply responsive styles to widget based on size class"""
        responsive_styles = {
            'small': {
                'padding': '8px',
                'font_size': '12px',
                'button_height': '32px'
            },
            'medium': {
                'padding': '12px',
                'font_size': '14px',
                'button_height': '36px'
            },
            'large': {
                'padding': '16px',
                'font_size': '14px',
                'button_height': '40px'
            },
            'extra_large': {
                'padding': '20px',
                'font_size': '16px',
                'button_height': '44px'
            }
        }
        
        styles = responsive_styles.get(size_class, responsive_styles['medium'])
        
        # Apply styles to widget
        # This would be implemented based on specific widget types
        pass
    
    def create_dark_stylesheet(self) -> str:
        """Create dark theme stylesheet"""
        # This would generate a dark version of the main stylesheet
        # For now, return a basic dark theme
        return """
        QWidget {
            background-color: #303030;
            color: #ffffff;
        }
        
        QPushButton {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
        }
        
        QPushButton:hover {
            background-color: #1976d2;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: #424242;
            color: #ffffff;
            border: 2px solid #616161;
            border-radius: 8px;
            padding: 12px 16px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border: 2px solid #2196f3;
        }
        
        QTableWidget {
            background-color: #424242;
            color: #ffffff;
            gridline-color: #616161;
        }
        
        QHeaderView::section {
            background-color: #303030;
            color: #ffffff;
            border: 1px solid #616161;
        }
        """
    
    def initialize_theme(self):
        """Initialize theme on application startup"""
        # Set initial theme
        self.set_theme(self.current_theme)
        
        # Set initial font size
        self.set_font_size(self.current_font_size)
        
        # Set RTL mode if enabled
        if self.rtl_mode:
            self.set_rtl_mode(True)
        
        self.logger.info("Theme manager initialized")


# Global theme manager instance
theme_manager = ThemeManager()


def get_theme_manager() -> ThemeManager:
    """Get global theme manager instance"""
    return theme_manager


def apply_theme(theme_name: str) -> bool:
    """Apply theme to application"""
    return theme_manager.set_theme(theme_name)


def get_current_colors() -> Dict[str, str]:
    """Get current theme colors"""
    return theme_manager.get_theme_colors()
