#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Impound management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QDialog, QFormLayout, QLineEdit, QComboBox, QMessageBox,
    QDateEdit, QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon

class ImpoundWidget(QWidget):
    """Impound management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize impound widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Gestion des Droits de Fourrière")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add vehicle button
        add_vehicle_btn = QPushButton("Ajouter un Véhicule")
        add_vehicle_btn.clicked.connect(self.add_vehicle)
        header_layout.addWidget(add_vehicle_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create current vehicles tab
        current_tab = QWidget()
        current_layout = QVBoxLayout(current_tab)
        
        # Create current vehicles table
        self.current_table = QTableWidget()
        self.current_table.setColumnCount(8)
        self.current_table.setHorizontalHeaderLabels([
            "Type", "Immatriculation", "Déposant", "Date d'entrée", 
            "Jours", "Total", "Actions", ""
        ])
        self.current_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.current_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)
        self.current_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeToContents)
        self.current_table.verticalHeader().setVisible(False)
        
        current_layout.addWidget(self.current_table)
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "Type", "Immatriculation", "Déposant", "Date d'entrée", 
            "Jours", "Total", "N° Créance", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeToContents)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create waiting for exit tab
        waiting_tab = QWidget()
        waiting_layout = QVBoxLayout(waiting_tab)
        
        # Create waiting for exit table
        self.waiting_table = QTableWidget()
        self.waiting_table.setColumnCount(9)
        self.waiting_table.setHorizontalHeaderLabels([
            "Type", "Immatriculation", "Déposant", "Date d'entrée", 
            "Jours", "Total", "N° Quittance", "Date Paiement", "Actions"
        ])
        self.waiting_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.waiting_table.horizontalHeader().setSectionResizeMode(8, QHeaderView.ResizeToContents)
        self.waiting_table.verticalHeader().setVisible(False)
        
        waiting_layout.addWidget(self.waiting_table)
        
        # Create exited vehicles tab
        exited_tab = QWidget()
        exited_layout = QVBoxLayout(exited_tab)
        
        # Create exited vehicles table
        self.exited_table = QTableWidget()
        self.exited_table.setColumnCount(10)
        self.exited_table.setHorizontalHeaderLabels([
            "Type", "Immatriculation", "Déposant", "Date d'entrée", 
            "Date de sortie", "Jours", "Total", "N° Quittance", "Date Paiement", "Actions"
        ])
        self.exited_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.exited_table.horizontalHeader().setSectionResizeMode(9, QHeaderView.ResizeToContents)
        self.exited_table.verticalHeader().setVisible(False)
        
        exited_layout.addWidget(self.exited_table)
        
        # Create 365+ days tab
        days365_tab = QWidget()
        days365_layout = QVBoxLayout(days365_tab)
        
        # Create 365+ days table
        self.days365_table = QTableWidget()
        self.days365_table.setColumnCount(8)
        self.days365_table.setHorizontalHeaderLabels([
            "Type", "Immatriculation", "Déposant", "Date d'entrée", 
            "Jours", "État", "Total", "Actions"
        ])
        self.days365_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.days365_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeToContents)
        self.days365_table.verticalHeader().setVisible(False)
        
        days365_layout.addWidget(self.days365_table)
        
        # Create auction tab
        auction_tab = QWidget()
        auction_layout = QVBoxLayout(auction_tab)
        
        # Create auction table
        self.auction_table = QTableWidget()
        self.auction_table.setColumnCount(9)
        self.auction_table.setHorizontalHeaderLabels([
            "Type", "Immatriculation", "Déposant", "Date d'entrée", 
            "Jours", "État", "N° Vente", "Date Vente", "Actions"
        ])
        self.auction_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.auction_table.horizontalHeader().setSectionResizeMode(8, QHeaderView.ResizeToContents)
        self.auction_table.verticalHeader().setVisible(False)
        
        auction_layout.addWidget(self.auction_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(current_tab, "Véhicules en Fourrière")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(waiting_tab, "En Attente de Sortie")
        self.tab_widget.addTab(exited_tab, "Véhicules Sortis")
        self.tab_widget.addTab(days365_tab, "Véhicules > 365 Jours")
        self.tab_widget.addTab(auction_tab, "Véhicules à Vendre")
        
        main_layout.addWidget(self.tab_widget)
        
        # Load data
        self.load_current_vehicles()
        self.load_pending_payments()
        self.load_waiting_vehicles()
        self.load_exited_vehicles()
        self.load_365days_vehicles()
        self.load_auction_vehicles()
    
    def load_current_vehicles(self):
        """Load current vehicles into the table"""
        # Clear table
        self.current_table.setRowCount(0)
        
        # Get current vehicles
        vehicles = self.db_manager.fetch_all(
            """
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
            FROM impound_vehicles v
            JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            JOIN depositor_types dt ON v.depositor_type_id = dt.id
            WHERE v.status = 'current'
            ORDER BY v.entry_date DESC
            """
        )
        
        # Add vehicles to table
        for i, vehicle in enumerate(vehicles):
            self.current_table.insertRow(i)
            
            # Calculate days and total
            entry_date = QDate.fromString(vehicle["entry_date"], "yyyy-MM-dd")
            current_date = QDate.currentDate()
            days = entry_date.daysTo(current_date)
            total = days * vehicle["daily_rate"]
            
            # Type
            type_item = QTableWidgetItem(vehicle["vehicle_type"])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.current_table.setItem(i, 0, type_item)
            
            # License plate
            license_item = QTableWidgetItem(vehicle["license_plate"])
            license_item.setFlags(license_item.flags() & ~Qt.ItemIsEditable)
            self.current_table.setItem(i, 1, license_item)
            
            # Depositor
            depositor_item = QTableWidgetItem(vehicle["depositor_type"])
            depositor_item.setFlags(depositor_item.flags() & ~Qt.ItemIsEditable)
            self.current_table.setItem(i, 2, depositor_item)
            
            # Entry date
            entry_item = QTableWidgetItem(vehicle["entry_date"])
            entry_item.setFlags(entry_item.flags() & ~Qt.ItemIsEditable)
            self.current_table.setItem(i, 3, entry_item)
            
            # Days
            days_item = QTableWidgetItem(str(days))
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            self.current_table.setItem(i, 4, days_item)
            
            # Total
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            self.current_table.setItem(i, 5, total_item)
            
            # Edit button
            edit_btn = QPushButton("Modifier")
            edit_btn.clicked.connect(lambda checked, v=vehicle: self.edit_vehicle(v))
            self.current_table.setCellWidget(i, 6, edit_btn)
            
            # Initiate payment button
            pay_btn = QPushButton("Initier Paiement")
            pay_btn.clicked.connect(lambda checked, v=vehicle: self.initiate_payment(v))
            self.current_table.setCellWidget(i, 7, pay_btn)
    
    def load_pending_payments(self):
        """Load pending payments into the table"""
        # Clear table
        self.pending_table.setRowCount(0)
        
        # Get pending payments
        vehicles = self.db_manager.fetch_all(
            """
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
            FROM impound_vehicles v
            JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            JOIN depositor_types dt ON v.depositor_type_id = dt.id
            WHERE v.status = 'pending_payment'
            ORDER BY v.entry_date DESC
            """
        )
        
        # Add vehicles to table
        for i, vehicle in enumerate(vehicles):
            self.pending_table.insertRow(i)
            
            # Calculate days and total
            entry_date = QDate.fromString(vehicle["entry_date"], "yyyy-MM-dd")
            current_date = QDate.currentDate()
            days = entry_date.daysTo(current_date)
            total = days * vehicle["daily_rate"]
            
            # Type
            type_item = QTableWidgetItem(vehicle["vehicle_type"])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 0, type_item)
            
            # License plate
            license_item = QTableWidgetItem(vehicle["license_plate"])
            license_item.setFlags(license_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 1, license_item)
            
            # Depositor
            depositor_item = QTableWidgetItem(vehicle["depositor_type"])
            depositor_item.setFlags(depositor_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 2, depositor_item)
            
            # Entry date
            entry_item = QTableWidgetItem(vehicle["entry_date"])
            entry_item.setFlags(entry_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 3, entry_item)
            
            # Days
            days_item = QTableWidgetItem(str(days))
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 4, days_item)
            
            # Total
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 5, total_item)
            
            # Claim number
            claim_item = QTableWidgetItem(vehicle["claim_number"])
            claim_item.setFlags(claim_item.flags() & ~Qt.ItemIsEditable)
            self.pending_table.setItem(i, 6, claim_item)
            
            # Validate payment button (only for regisseur)
            if self.auth.has_permission("validate_payment"):
                validate_btn = QPushButton("Valider Paiement")
                validate_btn.clicked.connect(lambda checked, v=vehicle: self.validate_payment(v))
                self.pending_table.setCellWidget(i, 7, validate_btn)
    
    def load_waiting_vehicles(self):
        """Load waiting vehicles into the table"""
        # Clear table
        self.waiting_table.setRowCount(0)
        
        # Get waiting vehicles
        vehicles = self.db_manager.fetch_all(
            """
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
            FROM impound_vehicles v
            JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            JOIN depositor_types dt ON v.depositor_type_id = dt.id
            WHERE v.status = 'waiting_exit'
            ORDER BY v.payment_date DESC
            """
        )
        
        # Add vehicles to table
        for i, vehicle in enumerate(vehicles):
            self.waiting_table.insertRow(i)
            
            # Calculate days and total
            entry_date = QDate.fromString(vehicle["entry_date"], "yyyy-MM-dd")
            payment_date = QDate.fromString(vehicle["payment_date"], "yyyy-MM-dd")
            days = entry_date.daysTo(payment_date)
            total = days * vehicle["daily_rate"]
            
            # Type
            type_item = QTableWidgetItem(vehicle["vehicle_type"])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 0, type_item)
            
            # License plate
            license_item = QTableWidgetItem(vehicle["license_plate"])
            license_item.setFlags(license_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 1, license_item)
            
            # Depositor
            depositor_item = QTableWidgetItem(vehicle["depositor_type"])
            depositor_item.setFlags(depositor_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 2, depositor_item)
            
            # Entry date
            entry_item = QTableWidgetItem(vehicle["entry_date"])
            entry_item.setFlags(entry_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 3, entry_item)
            
            # Days
            days_item = QTableWidgetItem(str(days))
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 4, days_item)
            
            # Total
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 5, total_item)
            
            # Receipt number
            receipt_item = QTableWidgetItem(vehicle["receipt_number"])
            receipt_item.setFlags(receipt_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 6, receipt_item)
            
            # Payment date
            payment_item = QTableWidgetItem(vehicle["payment_date"])
            payment_item.setFlags(payment_item.flags() & ~Qt.ItemIsEditable)
            self.waiting_table.setItem(i, 7, payment_item)
            
            # Exit button
            exit_btn = QPushButton("Sortie")
            exit_btn.clicked.connect(lambda checked, v=vehicle: self.exit_vehicle(v))
            self.waiting_table.setCellWidget(i, 8, exit_btn)
    
    def load_exited_vehicles(self):
        """Load exited vehicles into the table"""
        # Clear table
        self.exited_table.setRowCount(0)
        
        # Get exited vehicles
        vehicles = self.db_manager.fetch_all(
            """
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
            FROM impound_vehicles v
            JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            JOIN depositor_types dt ON v.depositor_type_id = dt.id
            WHERE v.status = 'exited'
            ORDER BY v.exit_date DESC
            LIMIT 100
            """
        )
        
        # Add vehicles to table
        for i, vehicle in enumerate(vehicles):
            self.exited_table.insertRow(i)
            
            # Calculate days and total
            entry_date = QDate.fromString(vehicle["entry_date"], "yyyy-MM-dd")
            exit_date = QDate.fromString(vehicle["exit_date"], "yyyy-MM-dd")
            days = entry_date.daysTo(exit_date)
            total = days * vehicle["daily_rate"]
            
            # Type
            type_item = QTableWidgetItem(vehicle["vehicle_type"])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 0, type_item)
            
            # License plate
            license_item = QTableWidgetItem(vehicle["license_plate"])
            license_item.setFlags(license_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 1, license_item)
            
            # Depositor
            depositor_item = QTableWidgetItem(vehicle["depositor_type"])
            depositor_item.setFlags(depositor_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 2, depositor_item)
            
            # Entry date
            entry_item = QTableWidgetItem(vehicle["entry_date"])
            entry_item.setFlags(entry_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 3, entry_item)
            
            # Exit date
            exit_item = QTableWidgetItem(vehicle["exit_date"])
            exit_item.setFlags(exit_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 4, exit_item)
            
            # Days
            days_item = QTableWidgetItem(str(days))
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 5, days_item)
            
            # Total
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 6, total_item)
            
            # Receipt number
            receipt_item = QTableWidgetItem(vehicle["receipt_number"])
            receipt_item.setFlags(receipt_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 7, receipt_item)
            
            # Payment date
            payment_item = QTableWidgetItem(vehicle["payment_date"])
            payment_item.setFlags(payment_item.flags() & ~Qt.ItemIsEditable)
            self.exited_table.setItem(i, 8, payment_item)
            
            # View details button
            view_btn = QPushButton("Détails")
            view_btn.clicked.connect(lambda checked, v=vehicle: self.view_vehicle_details(v))
            self.exited_table.setCellWidget(i, 9, view_btn)
    
    def load_365days_vehicles(self):
        """Load vehicles over 365 days into the table"""
        # Clear table
        self.days365_table.setRowCount(0)
        
        # Get vehicles over 365 days
        vehicles = self.db_manager.fetch_all(
            """
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
            FROM impound_vehicles v
            JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            JOIN depositor_types dt ON v.depositor_type_id = dt.id
            WHERE v.status = 'over_365_days'
            ORDER BY v.entry_date ASC
            """
        )
        
        # Add vehicles to table
        for i, vehicle in enumerate(vehicles):
            self.days365_table.insertRow(i)
            
            # Calculate days and total
            entry_date = QDate.fromString(vehicle["entry_date"], "yyyy-MM-dd")
            current_date = QDate.currentDate()
            days = entry_date.daysTo(current_date)
            total = days * vehicle["daily_rate"]
            
            # Type
            type_item = QTableWidgetItem(vehicle["vehicle_type"])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 0, type_item)
            
            # License plate
            license_item = QTableWidgetItem(vehicle["license_plate"])
            license_item.setFlags(license_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 1, license_item)
            
            # Depositor
            depositor_item = QTableWidgetItem(vehicle["depositor_type"])
            depositor_item.setFlags(depositor_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 2, depositor_item)
            
            # Entry date
            entry_item = QTableWidgetItem(vehicle["entry_date"])
            entry_item.setFlags(entry_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 3, entry_item)
            
            # Days
            days_item = QTableWidgetItem(str(days))
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 4, days_item)
            
            # Condition
            condition_item = QTableWidgetItem(vehicle["vehicle_condition"] or "Non spécifié")
            condition_item.setFlags(condition_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 5, condition_item)
            
            # Total
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            self.days365_table.setItem(i, 6, total_item)
            
            # Send to auction button
            auction_btn = QPushButton("Envoyer à la Vente")
            auction_btn.clicked.connect(lambda checked, v=vehicle: self.send_to_auction(v))
            self.days365_table.setCellWidget(i, 7, auction_btn)
    
    def load_auction_vehicles(self):
        """Load auction vehicles into the table"""
        # Clear table
        self.auction_table.setRowCount(0)
        
        # Get auction vehicles
        vehicles = self.db_manager.fetch_all(
            """
            SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
            FROM impound_vehicles v
            JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            JOIN depositor_types dt ON v.depositor_type_id = dt.id
            WHERE v.status = 'auction'
            ORDER BY v.auction_date DESC
            """
        )
        
        # Add vehicles to table
        for i, vehicle in enumerate(vehicles):
            self.auction_table.insertRow(i)
            
            # Calculate days
            entry_date = QDate.fromString(vehicle["entry_date"], "yyyy-MM-dd")
            current_date = QDate.currentDate()
            days = entry_date.daysTo(current_date)
            
            # Type
            type_item = QTableWidgetItem(vehicle["vehicle_type"])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 0, type_item)
            
            # License plate
            license_item = QTableWidgetItem(vehicle["license_plate"])
            license_item.setFlags(license_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 1, license_item)
            
            # Depositor
            depositor_item = QTableWidgetItem(vehicle["depositor_type"])
            depositor_item.setFlags(depositor_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 2, depositor_item)
            
            # Entry date
            entry_item = QTableWidgetItem(vehicle["entry_date"])
            entry_item.setFlags(entry_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 3, entry_item)
            
            # Days
            days_item = QTableWidgetItem(str(days))
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 4, days_item)
            
            # Condition
            condition_item = QTableWidgetItem(vehicle["vehicle_condition"] or "Non spécifié")
            condition_item.setFlags(condition_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 5, condition_item)
            
            # Auction number
            auction_num_item = QTableWidgetItem(vehicle["auction_number"] or "Non spécifié")
            auction_num_item.setFlags(auction_num_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 6, auction_num_item)
            
            # Auction date
            auction_date_item = QTableWidgetItem(vehicle["auction_date"] or "Non spécifié")
            auction_date_item.setFlags(auction_date_item.flags() & ~Qt.ItemIsEditable)
            self.auction_table.setItem(i, 7, auction_date_item)
            
            # Edit auction details button
            edit_btn = QPushButton("Modifier")
            edit_btn.clicked.connect(lambda checked, v=vehicle: self.edit_auction_details(v))
            self.auction_table.setCellWidget(i, 8, edit_btn)
    
    # Placeholder methods for impound actions
    def add_vehicle(self):
        """Add a new vehicle to the impound"""
        try:
            # Créer directement la boîte de dialogue sans import
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit, QTextEdit, QDialogButtonBox, QGroupBox
            from PyQt5.QtCore import Qt, QDate
            
            # Créer une boîte de dialogue simple
            dialog = QDialog(self)
            dialog.setWindowTitle("Ajouter un Véhicule")
            dialog.setMinimumWidth(500)
            dialog.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
            dialog.setStyleSheet("QDialog { background-color: #f5f5f5; }")
            
            # Créer le layout principal
            main_layout = QVBoxLayout(dialog)
            
            # Créer le groupe d'informations du véhicule
            vehicle_group = QGroupBox("Informations du Véhicule")
            vehicle_layout = QFormLayout(vehicle_group)
            
            # Type de véhicule
            type_combo = QComboBox()
            type_combo.addItems(["Voiture", "Camion", "Moto", "Vélo", "Autre"])
            vehicle_layout.addRow("Type de Véhicule:", type_combo)
            
            # Immatriculation
            plate_edit = QLineEdit()
            plate_edit.setPlaceholderText("Ex: 12345-A-6")
            vehicle_layout.addRow("Immatriculation:", plate_edit)
            
            # Marque et modèle
            brand_edit = QLineEdit()
            brand_edit.setPlaceholderText("Ex: Renault")
            vehicle_layout.addRow("Marque:", brand_edit)
            
            model_edit = QLineEdit()
            model_edit.setPlaceholderText("Ex: Clio")
            vehicle_layout.addRow("Modèle:", model_edit)
            
            # Couleur
            color_edit = QLineEdit()
            color_edit.setPlaceholderText("Ex: Bleu")
            vehicle_layout.addRow("Couleur:", color_edit)
            
            main_layout.addWidget(vehicle_group)
            
            # Créer le groupe d'informations du propriétaire
            owner_group = QGroupBox("Informations du Propriétaire")
            owner_layout = QFormLayout(owner_group)
            
            owner_name_edit = QLineEdit()
            owner_layout.addRow("Nom Complet:", owner_name_edit)
            
            owner_id_edit = QLineEdit()
            owner_layout.addRow("CIN:", owner_id_edit)
            
            owner_phone_edit = QLineEdit()
            owner_layout.addRow("Téléphone:", owner_phone_edit)
            
            main_layout.addWidget(owner_group)
            
            # Créer les boutons
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            
            main_layout.addWidget(button_box)
            
            # Afficher la boîte de dialogue
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Récupérer les données
                data = {
                    "type": type_combo.currentText(),
                    "plate": plate_edit.text(),
                    "brand": brand_edit.text(),
                    "model": model_edit.text(),
                    "color": color_edit.text(),
                    "owner_name": owner_name_edit.text(),
                    "owner_id": owner_id_edit.text(),
                    "owner_phone": owner_phone_edit.text()
                }
                
                # Vérifier les données
                if not data["plate"] or not data["owner_name"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir tous les champs obligatoires."
                    )
                    return
                
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Véhicule Ajouté",
                    f"Le véhicule {data['brand']} {data['model']} ({data['plate']}) a été ajouté avec succès."
                )
                self.load_current_vehicles()
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )
    
    def edit_vehicle(self, vehicle):
        """Edit an existing vehicle"""
        # TODO: Implement this
        pass
    
    def initiate_payment(self, vehicle):
        """Initiate payment for a vehicle"""
        # TODO: Implement this
        pass
    
    def validate_payment(self, vehicle):
        """Validate payment for a vehicle"""
        # TODO: Implement this
        pass
    
    def exit_vehicle(self, vehicle):
        """Process vehicle exit"""
        # TODO: Implement this
        pass
    
    def view_vehicle_details(self, vehicle):
        """View details of a vehicle"""
        # TODO: Implement this
        pass
    
    def send_to_auction(self, vehicle):
        """Send a vehicle to auction"""
        # TODO: Implement this
        pass
    
    def edit_auction_details(self, vehicle):
        """Edit auction details for a vehicle"""
        # TODO: Implement this
        pass