#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Temporary occupation management module for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class OccupationWidget(QWidget):
    """Temporary occupation management widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize occupation widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Gestion de la Redevance d'Occupation Temporaire")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Add occupation button
        add_occupation_btn = QPushButton("Ajouter une Occupation")
        add_occupation_btn.clicked.connect(self.add_occupation)
        header_layout.addWidget(add_occupation_btn)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create occupations tab
        occupations_tab = QWidget()
        occupations_layout = QVBoxLayout(occupations_tab)
        
        # Create occupations table
        self.occupations_table = QTableWidget()
        self.occupations_table.setColumnCount(9)
        self.occupations_table.setHorizontalHeaderLabels([
            "N° Dossier", "Propriétaire", "CIN/RC", "Type", 
            "Surface", "Adresse", "Téléphone", 
            "Dernier trimestre", "Total"
        ])
        self.occupations_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.occupations_table.verticalHeader().setVisible(False)
        
        occupations_layout.addWidget(self.occupations_table)
        
        # Create payment tab
        payment_tab = QWidget()
        payment_layout = QVBoxLayout(payment_tab)
        
        # TODO: Add payment interface
        payment_layout.addWidget(QLabel("Interface de paiement à implémenter"))
        
        # Create pending payments tab
        pending_tab = QWidget()
        pending_layout = QVBoxLayout(pending_tab)
        
        # Create pending payments table
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            "N° Dossier", "Propriétaire", "CIN/RC", "Type", 
            "Période", "Montant", "N° Créance", "Actions"
        ])
        self.pending_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.pending_table.verticalHeader().setVisible(False)
        
        pending_layout.addWidget(self.pending_table)
        
        # Create history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # Create history table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(7)
        self.history_table.setHorizontalHeaderLabels([
            "N° Dossier", "Propriétaire", "CIN/RC", 
            "Période", "Total", "Quittance", "Date"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.verticalHeader().setVisible(False)
        
        history_layout.addWidget(self.history_table)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(occupations_tab, "Liste des Occupations")
        self.tab_widget.addTab(payment_tab, "Initiation Paiement")
        self.tab_widget.addTab(pending_tab, "Paiements en Attente")
        self.tab_widget.addTab(history_tab, "Historique des Paiements")
        
        main_layout.addWidget(self.tab_widget)
    
    def add_occupation(self):
        """Add a new occupation"""
        try:
            # Créer directement la boîte de dialogue sans import
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QLabel, QLineEdit, QPushButton, QComboBox, QDoubleSpinBox, QDateEdit, QTextEdit, QDialogButtonBox, QGroupBox
            from PyQt5.QtCore import Qt, QDate
            
            # Créer une boîte de dialogue simple
            dialog = QDialog(self)
            dialog.setWindowTitle("Ajouter une Occupation Temporaire")
            dialog.setMinimumWidth(500)
            dialog.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
            dialog.setStyleSheet("QDialog { background-color: #f5f5f5; }")
            
            # Créer le layout principal
            main_layout = QVBoxLayout(dialog)
            
            # Créer le groupe d'informations de l'occupation
            occupation_group = QGroupBox("Informations de l'Occupation")
            occupation_layout = QFormLayout(occupation_group)
            
            # Type d'occupation
            type_combo = QComboBox()
            type_combo.addItems([
                "Terrasse de café", "Étalage commercial", "Kiosque", 
                "Chantier de construction", "Événement temporaire", "Autre"
            ])
            occupation_layout.addRow("Type d'Occupation:", type_combo)
            
            # Emplacement
            location_edit = QLineEdit()
            location_edit.setPlaceholderText("Ex: Avenue Mohammed V")
            occupation_layout.addRow("Emplacement:", location_edit)
            
            # Surface
            surface_spin = QDoubleSpinBox()
            surface_spin.setRange(1, 1000)
            surface_spin.setSuffix(" m²")
            occupation_layout.addRow("Surface:", surface_spin)
            
            # Période
            period_layout = QVBoxLayout()
            date_layout = QHBoxLayout()
            
            start_date_edit = QDateEdit()
            start_date_edit.setCalendarPopup(True)
            start_date_edit.setDate(QDate.currentDate())
            
            end_date_edit = QDateEdit()
            end_date_edit.setCalendarPopup(True)
            end_date_edit.setDate(QDate.currentDate().addMonths(1))
            
            date_layout.addWidget(QLabel("Du:"))
            date_layout.addWidget(start_date_edit)
            date_layout.addWidget(QLabel("Au:"))
            date_layout.addWidget(end_date_edit)
            
            period_layout.addLayout(date_layout)
            occupation_layout.addRow("Période:", period_layout)
            
            # Tarif
            rate_spin = QDoubleSpinBox()
            rate_spin.setRange(1, 10000)
            rate_spin.setSuffix(" DH/m²/jour")
            occupation_layout.addRow("Tarif:", rate_spin)
            
            main_layout.addWidget(occupation_group)
            
            # Créer le groupe d'informations du demandeur
            requester_group = QGroupBox("Informations du Demandeur")
            requester_layout = QFormLayout(requester_group)
            
            # Nom du demandeur
            requester_name_edit = QLineEdit()
            requester_layout.addRow("Nom Complet:", requester_name_edit)
            
            # CIN/RC
            requester_id_edit = QLineEdit()
            requester_layout.addRow("CIN/RC:", requester_id_edit)
            
            # Téléphone
            requester_phone_edit = QLineEdit()
            requester_layout.addRow("Téléphone:", requester_phone_edit)
            
            # Adresse
            requester_address_edit = QLineEdit()
            requester_layout.addRow("Adresse:", requester_address_edit)
            
            main_layout.addWidget(requester_group)
            
            # Créer les boutons
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            
            main_layout.addWidget(button_box)
            
            # Afficher la boîte de dialogue
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Récupérer les données
                data = {
                    "type": type_combo.currentText(),
                    "location": location_edit.text(),
                    "surface": surface_spin.value(),
                    "start_date": start_date_edit.date().toString("yyyy-MM-dd"),
                    "end_date": end_date_edit.date().toString("yyyy-MM-dd"),
                    "rate": rate_spin.value(),
                    "requester_name": requester_name_edit.text(),
                    "requester_id": requester_id_edit.text(),
                    "requester_phone": requester_phone_edit.text(),
                    "requester_address": requester_address_edit.text()
                }
                
                # Vérifier les données
                if not data["location"] or not data["requester_name"]:
                    QMessageBox.warning(
                        self,
                        "Champs Requis",
                        "Veuillez remplir tous les champs obligatoires."
                    )
                    return
                
                # TODO: Save to database
                QMessageBox.information(
                    self,
                    "Occupation Ajoutée",
                    f"L'occupation de type {data['type']} à {data['location']} a été ajoutée avec succès."
                )
                # Reload data
                # TODO: Implement reload method
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur s'est produite: {str(e)}"
            )