#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for the Fiscal Resources Management Application
Handles all database operations and connections
"""

import os
import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from contextlib import contextmanager

# Import our custom utilities
from utils.cache import cache_manager, cached, cache_key_for_query
from utils.logging_config import get_logger, log_database_query, PerformanceTimer

class DatabaseManager:
    """Database manager class for SQLite operations with improved error handling"""

    def __init__(self, db_path="database/fiscal_resources.db"):
        """Initialize database manager"""
        self.db_path = db_path
        self.connection = None

        # Setup logging with our custom logger
        self.logger = get_logger("database")
        self.cursor = None

        # Get cache for database operations
        self.cache = cache_manager.get_cache('database')
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.connection.cursor()
            return True
        except sqlite3.Error as e:
            self.logger.error(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """Close the database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.cursor = None
    
    def execute_query(self, query, params=None):
        """Execute a query with optional parameters"""
        try:
            if not self.connection:
                self.connect()
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Query execution error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            return False
    
    @log_database_query()
    def fetch_all(self, query, params=None, use_cache=True):
        """Execute a query and fetch all results with caching"""
        # Generate cache key
        cache_key = cache_key_for_query(query, params or ())

        # Try cache first if enabled
        if use_cache and self.cache:
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                self.logger.debug(f"Cache hit for query: {query[:50]}...")
                return cached_result

        try:
            with PerformanceTimer(f"fetch_all: {query[:50]}...", self.logger):
                if not self.connection:
                    self.connect()

                if params:
                    self.cursor.execute(query, params)
                else:
                    self.cursor.execute(query)

                result = self.cursor.fetchall()

                # Cache the result if caching is enabled
                if use_cache and self.cache:
                    self.cache.set(cache_key, result, ttl=300)  # 5 minutes

                return result

        except sqlite3.Error as e:
            self.logger.error(f"Query execution error: {e}")
            self.logger.error(f"Query: {query}")
            self.logger.error(f"Params: {params}")
            return []
    
    @log_database_query()
    def fetch_one(self, query, params=None, use_cache=True):
        """Execute a query and fetch one result with caching"""
        # Generate cache key
        cache_key = cache_key_for_query(query, params or ())

        # Try cache first if enabled
        if use_cache and self.cache:
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                self.logger.debug(f"Cache hit for query: {query[:50]}...")
                return cached_result

        try:
            with PerformanceTimer(f"fetch_one: {query[:50]}...", self.logger):
                if not self.connection:
                    self.connect()

                if params:
                    self.cursor.execute(query, params)
                else:
                    self.cursor.execute(query)

                result = self.cursor.fetchone()

                # Cache the result if caching is enabled
                if use_cache and self.cache:
                    self.cache.set(cache_key, result, ttl=300)  # 5 minutes

                return result

        except sqlite3.Error as e:
            self.logger.error(f"Query execution error: {e}")
            self.logger.error(f"Query: {query}")
            self.logger.error(f"Params: {params}")
            return None
    
    def initialize_database(self):
        """Initialize the database with all required tables"""
        if not os.path.exists(os.path.dirname(self.db_path)):
            os.makedirs(os.path.dirname(self.db_path))
        
        self.connect()
        
        # Create users table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        ''')
        
        # Create configuration table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS configuration (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT NOT NULL,
            UNIQUE(category, key)
        )
        ''')
        
        # Create vehicle types table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS vehicle_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            daily_rate REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create depositor types table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS depositor_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create impound vehicles table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS impound_vehicles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            vehicle_type_id INTEGER NOT NULL,
            license_plate TEXT NOT NULL,
            brand TEXT,
            model TEXT,
            color TEXT,
            depositor_type_id INTEGER NOT NULL,
            owner_name TEXT NOT NULL,
            owner_id TEXT,
            owner_phone TEXT,
            entry_date DATE NOT NULL,
            status TEXT NOT NULL DEFAULT 'current',
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            exit_date DATE,
            vehicle_condition TEXT,
            auction_number TEXT,
            auction_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_type_id) REFERENCES vehicle_types (id),
            FOREIGN KEY (depositor_type_id) REFERENCES depositor_types (id)
        )
        ''')
        
        # Create sectors table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS sectors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create commercial shops table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS commercial_shops (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sector_id INTEGER NOT NULL,
            shop_number TEXT NOT NULL,
            surface REAL NOT NULL,
            status TEXT NOT NULL DEFAULT 'constructed',
            owner_name TEXT,
            id_number TEXT,
            address TEXT,
            phone TEXT,
            occupation_date DATE,
            monthly_rate REAL NOT NULL,
            last_rate_increase_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sector_id) REFERENCES sectors (id)
        )
        ''')
        
        # Create shop payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS shop_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shop_id INTEGER NOT NULL,
            start_month DATE NOT NULL,
            end_month DATE NOT NULL,
            months_count INTEGER NOT NULL,
            amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (shop_id) REFERENCES commercial_shops (id)
        )
        ''')
        
        # Create souk leases table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS souk_leases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tenant_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            monthly_amount REAL NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create souk payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS souk_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lease_id INTEGER NOT NULL,
            start_month DATE NOT NULL,
            end_month DATE NOT NULL,
            months_count INTEGER NOT NULL,
            amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (lease_id) REFERENCES souk_leases (id)
        )
        ''')
        
        # Create beverage establishments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS beverage_establishments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT NOT NULL UNIQUE,
            establishment_name TEXT NOT NULL,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            patent_number TEXT,
            address TEXT,
            phone TEXT,
            annual_declaration_date DATE,
            status TEXT NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create beverage payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS beverage_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            establishment_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL,
            revenue REAL NOT NULL,
            odp_amount REAL DEFAULT 0,
            declaration_default BOOLEAN DEFAULT FALSE,
            tax_amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (establishment_id) REFERENCES beverage_establishments (id)
        )
        ''')
        
        # Create parking licenses table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS parking_licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_number TEXT NOT NULL UNIQUE,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            vehicle_type_id INTEGER NOT NULL,
            license_plate TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_type_id) REFERENCES vehicle_types (id)
        )
        ''')
        
        # Create parking payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS parking_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL,
            transport_tax REAL NOT NULL,
            parking_fee REAL NOT NULL,
            total_amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (license_id) REFERENCES parking_licenses (id)
        )
        ''')
        
        # Create temporary occupation types table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS occupation_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            rate_per_sqm REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create temporary occupations table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS temporary_occupations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT NOT NULL UNIQUE,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            occupation_type_id INTEGER NOT NULL,
            surface REAL NOT NULL,
            address TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (occupation_type_id) REFERENCES occupation_types (id)
        )
        ''')
        
        # Create occupation payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS occupation_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            occupation_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL,
            amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (occupation_id) REFERENCES temporary_occupations (id)
        )
        ''')
        
        # Create zones table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS zones (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_fr TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            rate_per_sqm REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create undeveloped lands table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS undeveloped_lands (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT NOT NULL UNIQUE,
            location TEXT NOT NULL,
            lot_number TEXT,
            title_deed TEXT,
            surface REAL NOT NULL,
            zone_id INTEGER NOT NULL,
            owner_name TEXT NOT NULL,
            id_number TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            acquisition_date DATE,
            status TEXT NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (zone_id) REFERENCES zones (id)
        )
        ''')
        
        # Create land payments table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS land_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            land_id INTEGER NOT NULL,
            year INTEGER NOT NULL,
            amount REAL NOT NULL,
            penalty REAL DEFAULT 0,
            surcharge REAL DEFAULT 0,
            fine REAL DEFAULT 0,
            total_amount REAL NOT NULL,
            claim_number TEXT,
            receipt_number TEXT,
            payment_date DATE,
            status TEXT NOT NULL DEFAULT 'initiated',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (land_id) REFERENCES undeveloped_lands (id)
        )
        ''')
        
        # Create land census table
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS land_census (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            location TEXT NOT NULL,
            lot_number TEXT,
            title_deed TEXT,
            surface REAL NOT NULL,
            zone_id INTEGER NOT NULL,
            owner_name TEXT,
            id_number TEXT,
            address TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (zone_id) REFERENCES zones (id)
        )
        ''')
        
        # Insert default admin user if not exists
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = ?", ("admin",))
        if not admin_exists:
            self.execute_query(
                "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
                ("admin", "8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918", "Administrator", "admin")
            )  # Password: admin (SHA-256 hashed)

        # Insert default vehicle types if not exists
        vehicle_types_exist = self.fetch_one("SELECT id FROM vehicle_types LIMIT 1")
        if not vehicle_types_exist:
            vehicle_types = [
                ("Voiture", "سيارة", 50.0),
                ("Camion", "شاحنة", 100.0),
                ("Moto", "دراجة نارية", 25.0),
                ("Vélo", "دراجة هوائية", 10.0),
                ("Autre", "أخرى", 30.0)
            ]
            for name_fr, name_ar, rate in vehicle_types:
                self.execute_query(
                    "INSERT INTO vehicle_types (name_fr, name_ar, daily_rate) VALUES (?, ?, ?)",
                    (name_fr, name_ar, rate)
                )

        # Insert default depositor types if not exists
        depositor_types_exist = self.fetch_one("SELECT id FROM depositor_types LIMIT 1")
        if not depositor_types_exist:
            depositor_types = [
                ("Police", "الشرطة"),
                ("Gendarmerie", "الدرك الملكي"),
                ("Autorité Locale", "السلطة المحلية"),
                ("Autre", "أخرى")
            ]
            for name_fr, name_ar in depositor_types:
                self.execute_query(
                    "INSERT INTO depositor_types (name_fr, name_ar) VALUES (?, ?)",
                    (name_fr, name_ar)
                )

        # Insert default sectors if not exists
        sectors_exist = self.fetch_one("SELECT id FROM sectors LIMIT 1")
        if not sectors_exist:
            sectors = [
                ("Secteur 1", "القطاع 1"),
                ("Secteur 2", "القطاع 2"),
                ("Secteur 3", "القطاع 3"),
                ("Secteur 4", "القطاع 4")
            ]
            for name_fr, name_ar in sectors:
                self.execute_query(
                    "INSERT INTO sectors (name_fr, name_ar) VALUES (?, ?)",
                    (name_fr, name_ar)
                )

        # Insert default zones if not exists
        zones_exist = self.fetch_one("SELECT id FROM zones LIMIT 1")
        if not zones_exist:
            zones = [
                ("Zone Résidentielle", "المنطقة السكنية", 15.0),
                ("Zone Commerciale", "المنطقة التجارية", 25.0),
                ("Zone Industrielle", "المنطقة الصناعية", 20.0),
                ("Zone Touristique", "المنطقة السياحية", 30.0)
            ]
            for name_fr, name_ar, rate in zones:
                self.execute_query(
                    "INSERT INTO zones (name_fr, name_ar, rate_per_sqm) VALUES (?, ?, ?)",
                    (name_fr, name_ar, rate)
                )

        # Insert default occupation types if not exists
        occupation_types_exist = self.fetch_one("SELECT id FROM occupation_types LIMIT 1")
        if not occupation_types_exist:
            occupation_types = [
                ("Terrasse de café", "شرفة مقهى", 50.0),
                ("Étalage commercial", "عرض تجاري", 30.0),
                ("Kiosque", "كشك", 40.0),
                ("Chantier", "ورش", 20.0),
                ("Autre", "أخرى", 25.0)
            ]
            for name_fr, name_ar, rate in occupation_types:
                self.execute_query(
                    "INSERT INTO occupation_types (name_fr, name_ar, rate_per_sqm) VALUES (?, ?, ?)",
                    (name_fr, name_ar, rate)
                )
        
        self.disconnect()

    def validate_input(self, data: Dict[str, Any], required_fields: List[str]) -> bool:
        """Validate input data for required fields"""
        for field in required_fields:
            if field not in data or not data[field]:
                self.logger.warning(f"Missing required field: {field}")
                return False
        return True

    def sanitize_input(self, value: str) -> str:
        """Basic input sanitization"""
        if not isinstance(value, str):
            return str(value)
        # Remove potentially dangerous characters and SQL injection patterns
        dangerous_patterns = [';', '--', 'DROP', 'DELETE', 'INSERT', 'UPDATE', 'SELECT']
        sanitized = value.strip()
        for pattern in dangerous_patterns:
            sanitized = sanitized.replace(pattern, '')
        return sanitized

    # Vehicle Management Methods
    def create_vehicle(self, vehicle_data: Dict[str, Any]) -> Optional[int]:
        """Create a new vehicle record with validation"""
        required_fields = ['vehicle_type_id', 'license_plate', 'depositor_type_id', 'owner_name']

        if not self.validate_input(vehicle_data, required_fields):
            return None

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Check if license plate already exists
                cursor.execute(
                    "SELECT id FROM impound_vehicles WHERE license_plate = ? AND status != 'exited'",
                    (vehicle_data['license_plate'],)
                )

                if cursor.fetchone():
                    self.logger.warning(f"Vehicle with plate {vehicle_data['license_plate']} already exists")
                    return None

                # Insert new vehicle
                cursor.execute('''
                    INSERT INTO impound_vehicles (
                        vehicle_type_id, license_plate, brand, model, color,
                        depositor_type_id, owner_name, owner_id, owner_phone,
                        entry_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'current')
                ''', (
                    vehicle_data['vehicle_type_id'],
                    self.sanitize_input(vehicle_data['license_plate']),
                    self.sanitize_input(vehicle_data.get('brand', '')),
                    self.sanitize_input(vehicle_data.get('model', '')),
                    self.sanitize_input(vehicle_data.get('color', '')),
                    vehicle_data['depositor_type_id'],
                    self.sanitize_input(vehicle_data['owner_name']),
                    self.sanitize_input(vehicle_data.get('owner_id', '')),
                    self.sanitize_input(vehicle_data.get('owner_phone', '')),
                    vehicle_data.get('entry_date', datetime.now().strftime('%Y-%m-%d'))
                ))

                conn.commit()
                return cursor.lastrowid

        except sqlite3.Error as e:
            self.logger.error(f"Error creating vehicle: {e}")
            return None

    def update_vehicle(self, vehicle_id: int, vehicle_data: Dict[str, Any]) -> bool:
        """Update vehicle record"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Build update query dynamically
                update_fields = []
                values = []

                for field, value in vehicle_data.items():
                    if field != 'id':
                        update_fields.append(f"{field} = ?")
                        values.append(self.sanitize_input(str(value)) if isinstance(value, str) else value)

                if not update_fields:
                    return False

                values.append(vehicle_id)
                query = f"UPDATE impound_vehicles SET {', '.join(update_fields)} WHERE id = ?"

                cursor.execute(query, values)
                conn.commit()

                return cursor.rowcount > 0

        except sqlite3.Error as e:
            self.logger.error(f"Error updating vehicle: {e}")
            return False

    def delete_vehicle(self, vehicle_id: int) -> bool:
        """Delete vehicle record"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM impound_vehicles WHERE id = ?", (vehicle_id,))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.Error as e:
            self.logger.error(f"Error deleting vehicle: {e}")
            return False

    def get_vehicle_by_id(self, vehicle_id: int) -> Optional[Dict[str, Any]]:
        """Get vehicle by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT v.*, vt.name_fr as vehicle_type, dt.name_fr as depositor_type, vt.daily_rate
                    FROM impound_vehicles v
                    JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
                    JOIN depositor_types dt ON v.depositor_type_id = dt.id
                    WHERE v.id = ?
                ''', (vehicle_id,))

                row = cursor.fetchone()
                return dict(row) if row else None
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching vehicle: {e}")
            return None

    # Shop Management Methods
    def create_shop(self, shop_data: Dict[str, Any]) -> Optional[int]:
        """Create a new commercial shop record"""
        required_fields = ['sector_id', 'shop_number', 'surface', 'owner_name', 'owner_id']

        if not self.validate_input(shop_data, required_fields):
            return None

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Check if shop number already exists in the same sector
                cursor.execute(
                    "SELECT id FROM commercial_shops WHERE sector_id = ? AND shop_number = ?",
                    (shop_data['sector_id'], shop_data['shop_number'])
                )

                if cursor.fetchone():
                    self.logger.warning(f"Shop {shop_data['shop_number']} already exists in this sector")
                    return None

                # Calculate monthly rate (default rate per m²)
                monthly_rate = shop_data.get('monthly_rate', shop_data['surface'] * 50.0)  # 50 DH per m²

                # Insert new shop
                cursor.execute('''
                    INSERT INTO commercial_shops (
                        sector_id, shop_number, surface, status, owner_name, id_number,
                        address, phone, occupation_date, monthly_rate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    shop_data['sector_id'],
                    self.sanitize_input(shop_data['shop_number']),
                    shop_data['surface'],
                    shop_data.get('status', 'constructed'),
                    self.sanitize_input(shop_data['owner_name']),
                    self.sanitize_input(shop_data['owner_id']),
                    self.sanitize_input(shop_data.get('address', '')),
                    self.sanitize_input(shop_data.get('phone', '')),
                    shop_data.get('occupation_date', datetime.now().strftime('%Y-%m-%d')),
                    monthly_rate
                ))

                conn.commit()
                return cursor.lastrowid

        except sqlite3.Error as e:
            self.logger.error(f"Error creating shop: {e}")
            return None

    def get_shops_by_owner(self, owner_id: str) -> List[Dict[str, Any]]:
        """Get all shops owned by a specific person"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT s.*, sec.name_fr as sector_name
                    FROM commercial_shops s
                    JOIN sectors sec ON s.sector_id = sec.id
                    WHERE s.id_number = ?
                    ORDER BY s.shop_number
                ''', (owner_id,))

                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching shops by owner: {e}")
            return []

    def get_unpaid_shops(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Get shops with unpaid months (with pagination)"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT s.*, sec.name_fr as sector_name,
                           COALESCE(MAX(sp.end_month), s.occupation_date) as last_paid_month,
                           CASE
                               WHEN MAX(sp.end_month) IS NULL THEN
                                   CAST((julianday('now') - julianday(s.occupation_date)) / 30 AS INTEGER)
                               ELSE
                                   CAST((julianday('now') - julianday(MAX(sp.end_month))) / 30 AS INTEGER)
                           END as unpaid_months
                    FROM commercial_shops s
                    JOIN sectors sec ON s.sector_id = sec.id
                    LEFT JOIN shop_payments sp ON s.id = sp.shop_id AND sp.status = 'validated'
                    GROUP BY s.id
                    HAVING unpaid_months > 0
                    ORDER BY unpaid_months DESC, s.shop_number
                    LIMIT ? OFFSET ?
                ''', (limit, offset))

                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching unpaid shops: {e}")
            return []

    # Payment Management Methods
    def initiate_payment(self, table: str, record_id: int, payment_data: Dict[str, Any]) -> Optional[int]:
        """Generic method to initiate payment for any module"""
        required_fields = ['amount', 'claim_number']

        if not self.validate_input(payment_data, required_fields):
            return None

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Determine the correct payment table and foreign key
                payment_tables = {
                    'impound_vehicles': ('vehicle_payments', 'vehicle_id'),
                    'commercial_shops': ('shop_payments', 'shop_id'),
                    'souk_leases': ('souk_payments', 'lease_id'),
                    'beverage_establishments': ('beverage_payments', 'establishment_id'),
                    'parking_licenses': ('parking_payments', 'license_id'),
                    'temporary_occupations': ('occupation_payments', 'occupation_id'),
                    'undeveloped_lands': ('land_payments', 'land_id')
                }

                if table not in payment_tables:
                    self.logger.error(f"Unknown table: {table}")
                    return None

                payment_table, foreign_key = payment_tables[table]

                # Insert payment record
                cursor.execute(f'''
                    INSERT INTO {payment_table} (
                        {foreign_key}, amount, claim_number, status, created_at
                    ) VALUES (?, ?, ?, 'initiated', ?)
                ''', (
                    record_id,
                    payment_data['amount'],
                    self.sanitize_input(payment_data['claim_number']),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))

                conn.commit()
                return cursor.lastrowid

        except sqlite3.Error as e:
            self.logger.error(f"Error initiating payment: {e}")
            return None

    def validate_payment(self, payment_table: str, payment_id: int, receipt_data: Dict[str, Any]) -> bool:
        """Validate a payment with receipt information"""
        required_fields = ['receipt_number', 'payment_date']

        if not self.validate_input(receipt_data, required_fields):
            return False

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute(f'''
                    UPDATE {payment_table}
                    SET receipt_number = ?, payment_date = ?, status = 'validated',
                        updated_at = ?
                    WHERE id = ? AND status = 'initiated'
                ''', (
                    self.sanitize_input(receipt_data['receipt_number']),
                    receipt_data['payment_date'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    payment_id
                ))

                conn.commit()
                return cursor.rowcount > 0

        except sqlite3.Error as e:
            self.logger.error(f"Error validating payment: {e}")
            return False

    # Cache Management Methods
    def invalidate_cache(self, pattern: str = None):
        """Invalidate cache entries matching pattern"""
        if not self.cache:
            return

        if pattern:
            # Invalidate specific pattern
            keys_to_remove = [key for key in self.cache.get_keys() if pattern in key]
            for key in keys_to_remove:
                self.cache.delete(key)
            self.logger.info(f"Invalidated {len(keys_to_remove)} cache entries matching '{pattern}'")
        else:
            # Clear all cache
            self.cache.clear()
            self.logger.info("Cleared all database cache")

    def invalidate_table_cache(self, table_name: str):
        """Invalidate cache for specific table"""
        self.invalidate_cache(table_name)

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get database cache statistics"""
        if self.cache:
            return self.cache.get_stats()
        return {}

    # Pagination Methods
    def fetch_paginated(self, query: str, params: tuple = None, page: int = 1,
                       page_size: int = 50, use_cache: bool = True) -> Dict[str, Any]:
        """Fetch paginated results"""
        offset = (page - 1) * page_size

        # Count total records
        count_query = f"SELECT COUNT(*) FROM ({query})"
        total_count = self.fetch_one(count_query, params, use_cache=use_cache)
        total_records = total_count[0] if total_count else 0

        # Fetch page data
        paginated_query = f"{query} LIMIT {page_size} OFFSET {offset}"
        records = self.fetch_all(paginated_query, params, use_cache=use_cache)

        # Calculate pagination info
        total_pages = (total_records + page_size - 1) // page_size
        has_next = page < total_pages
        has_prev = page > 1

        return {
            'records': records,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_next': has_next,
                'has_prev': has_prev
            }
        }

    # Bulk Operations
    def bulk_insert(self, table: str, records: List[Dict[str, Any]],
                   batch_size: int = 100) -> bool:
        """Insert multiple records in batches"""
        if not records:
            return True

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Get column names from first record
                columns = list(records[0].keys())
                placeholders = ', '.join(['?' for _ in columns])
                query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"

                # Process in batches
                for i in range(0, len(records), batch_size):
                    batch = records[i:i + batch_size]
                    batch_data = [tuple(record[col] for col in columns) for record in batch]

                    cursor.executemany(query, batch_data)
                    self.logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} records")

                conn.commit()

                # Invalidate cache for this table
                self.invalidate_table_cache(table)

                self.logger.info(f"Successfully inserted {len(records)} records into {table}")
                return True

        except sqlite3.Error as e:
            self.logger.error(f"Error in bulk insert: {e}")
            return False

    def bulk_update(self, table: str, updates: List[Dict[str, Any]],
                   id_column: str = 'id') -> bool:
        """Update multiple records"""
        if not updates:
            return True

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                for update_data in updates:
                    record_id = update_data.pop(id_column)

                    if update_data:  # Only update if there are fields to update
                        set_clause = ', '.join([f"{col} = ?" for col in update_data.keys()])
                        query = f"UPDATE {table} SET {set_clause} WHERE {id_column} = ?"

                        values = list(update_data.values()) + [record_id]
                        cursor.execute(query, values)

                conn.commit()

                # Invalidate cache for this table
                self.invalidate_table_cache(table)

                self.logger.info(f"Successfully updated {len(updates)} records in {table}")
                return True

        except sqlite3.Error as e:
            self.logger.error(f"Error in bulk update: {e}")
            return False