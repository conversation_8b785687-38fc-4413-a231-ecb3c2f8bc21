#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Caching system for the Fiscal Resources Management Application
Provides in-memory caching with TTL (Time To Live) support
"""

import time
import threading
from typing import Any, Dict, Optional, Callable
from dataclasses import dataclass
import logging


@dataclass
class CacheEntry:
    """Cache entry with value and metadata"""
    value: Any
    created_at: float
    ttl: float
    access_count: int = 0
    last_accessed: float = 0.0
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        return time.time() - self.created_at > self.ttl
    
    def access(self) -> Any:
        """Access the cached value and update metadata"""
        self.access_count += 1
        self.last_accessed = time.time()
        return self.value


class Cache:
    """Thread-safe in-memory cache with TTL support"""
    
    def __init__(self, default_ttl: float = 300.0, max_size: int = 1000):
        """
        Initialize cache
        
        Args:
            default_ttl: Default time to live in seconds (5 minutes)
            max_size: Maximum number of entries in cache
        """
        self.default_ttl = default_ttl
        self.max_size = max_size
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self._hits = 0
        self._misses = 0
        self._evictions = 0
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            if key not in self._cache:
                self._misses += 1
                return None
            
            entry = self._cache[key]
            
            if entry.is_expired():
                del self._cache[key]
                self._misses += 1
                return None
            
            self._hits += 1
            return entry.access()
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Set value in cache"""
        if ttl is None:
            ttl = self.default_ttl
        
        with self._lock:
            # Check if we need to evict entries
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            entry = CacheEntry(
                value=value,
                created_at=time.time(),
                ttl=ttl,
                last_accessed=time.time()
            )
            
            self._cache[key] = entry
    
    def delete(self, key: str) -> bool:
        """Delete entry from cache"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._hits = 0
            self._misses = 0
            self._evictions = 0
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count of removed entries"""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
            
            return len(expired_keys)
    
    def _evict_lru(self) -> None:
        """Evict least recently used entry"""
        if not self._cache:
            return
        
        # Find LRU entry
        lru_key = min(
            self._cache.keys(),
            key=lambda k: self._cache[k].last_accessed
        )
        
        del self._cache[lru_key]
        self._evictions += 1
    
    def get_or_set(self, key: str, fetch_func: Callable[[], Any], ttl: Optional[float] = None) -> Any:
        """Get value from cache or fetch and cache it"""
        value = self.get(key)
        
        if value is None:
            value = fetch_func()
            self.set(key, value, ttl)
        
        return value
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = (self._hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': round(hit_rate, 2),
                'evictions': self._evictions,
                'expired_entries': sum(1 for entry in self._cache.values() if entry.is_expired())
            }
    
    def get_keys(self) -> list:
        """Get all cache keys"""
        with self._lock:
            return list(self._cache.keys())


class CacheManager:
    """Global cache manager for the application"""
    
    def __init__(self):
        """Initialize cache manager"""
        self.caches: Dict[str, Cache] = {}
        self.logger = logging.getLogger(__name__)
        
        # Create default caches
        self.create_cache('database', default_ttl=300.0, max_size=500)  # 5 minutes
        self.create_cache('ui', default_ttl=60.0, max_size=100)         # 1 minute
        self.create_cache('config', default_ttl=3600.0, max_size=50)    # 1 hour
    
    def create_cache(self, name: str, default_ttl: float = 300.0, max_size: int = 1000) -> Cache:
        """Create a new named cache"""
        cache = Cache(default_ttl=default_ttl, max_size=max_size)
        self.caches[name] = cache
        self.logger.info(f"Created cache '{name}' with TTL={default_ttl}s, max_size={max_size}")
        return cache
    
    def get_cache(self, name: str) -> Optional[Cache]:
        """Get cache by name"""
        return self.caches.get(name)
    
    def clear_all(self) -> None:
        """Clear all caches"""
        for cache in self.caches.values():
            cache.clear()
        self.logger.info("Cleared all caches")
    
    def cleanup_all_expired(self) -> Dict[str, int]:
        """Cleanup expired entries in all caches"""
        results = {}
        for name, cache in self.caches.items():
            expired_count = cache.cleanup_expired()
            results[name] = expired_count
            if expired_count > 0:
                self.logger.info(f"Cleaned up {expired_count} expired entries from cache '{name}'")
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all caches"""
        return {name: cache.get_stats() for name, cache in self.caches.items()}


# Global cache manager instance
cache_manager = CacheManager()


def cached(cache_name: str = 'database', ttl: Optional[float] = None, key_func: Optional[Callable] = None):
    """
    Decorator for caching function results
    
    Args:
        cache_name: Name of cache to use
        ttl: Time to live for cached result
        key_func: Function to generate cache key from function args
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            cache = cache_manager.get_cache(cache_name)
            if not cache:
                # No cache available, execute function directly
                return func(*args, **kwargs)
            
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


def cache_key_for_query(query: str, params: tuple = ()) -> str:
    """Generate cache key for database query"""
    return f"query:{hash(query + str(params))}"


def cache_key_for_user_data(user_id: int, data_type: str) -> str:
    """Generate cache key for user-specific data"""
    return f"user:{user_id}:{data_type}"


def cache_key_for_module_data(module: str, data_type: str, filters: Dict[str, Any] = None) -> str:
    """Generate cache key for module data"""
    filter_str = str(sorted(filters.items())) if filters else ""
    return f"module:{module}:{data_type}:{hash(filter_str)}"
