#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Accessibility features for the Fiscal Resources Management Application
Provides keyboard navigation, screen reader support, and accessibility enhancements
"""

from PyQt5.QtWidgets import (
    QWidget, QApplication, QShortcut, QLabel, QPushButton, 
    QTableWidget, QLineEdit, QComboBox, QCheckBox, QRadioButton
)
from PyQt5.QtCore import Qt, QObject, pyqtSignal, QTimer
from PyQt5.QtGui import QKeySequence, QFont, QPalette, QColor
from typing import List, Dict, Any, Optional
from utils.logging_config import get_logger


class AccessibilityManager(QObject):
    """Manages accessibility features for the application"""
    
    # Signals
    focus_changed = pyqtSignal(QWidget)
    accessibility_mode_changed = pyqtSignal(bool)
    
    def __init__(self, main_window):
        """Initialize accessibility manager"""
        super().__init__()
        
        self.main_window = main_window
        self.logger = get_logger("accessibility")
        
        # Accessibility settings
        self.high_contrast_mode = False
        self.large_text_mode = False
        self.keyboard_navigation_mode = False
        self.screen_reader_mode = False
        
        # Focus tracking
        self.focus_history = []
        self.current_focus_index = -1
        
        # Keyboard shortcuts
        self.shortcuts = {}
        
        self.setup_accessibility_features()
    
    def setup_accessibility_features(self):
        """Setup accessibility features"""
        # Setup keyboard navigation
        self.setup_keyboard_navigation()
        
        # Setup focus tracking
        self.setup_focus_tracking()
        
        # Setup screen reader support
        self.setup_screen_reader_support()
        
        # Setup high contrast mode
        self.setup_high_contrast_mode()
        
        self.logger.info("Accessibility features initialized")
    
    def setup_keyboard_navigation(self):
        """Setup keyboard navigation shortcuts"""
        # Global navigation shortcuts
        shortcuts = {
            'Alt+1': lambda: self.navigate_to_page(0),  # Dashboard
            'Alt+2': lambda: self.navigate_to_page(2),  # Impound
            'Alt+3': lambda: self.navigate_to_page(3),  # Shops
            'Alt+4': lambda: self.navigate_to_page(4),  # Souks
            'Alt+5': lambda: self.navigate_to_page(5),  # Beverages
            'Alt+6': lambda: self.navigate_to_page(6),  # Parking
            'Alt+7': lambda: self.navigate_to_page(7),  # Occupation
            'Alt+8': lambda: self.navigate_to_page(8),  # Lands
            'Alt+9': lambda: self.navigate_to_page(1),  # Admin
            'Ctrl+Shift+H': self.toggle_high_contrast,
            'Ctrl+Shift+L': self.toggle_large_text,
            'Ctrl+Shift+K': self.toggle_keyboard_navigation,
            'F1': self.show_help,
            'Escape': self.handle_escape,
            'Tab': self.handle_tab_navigation,
            'Shift+Tab': self.handle_shift_tab_navigation,
        }
        
        for key_sequence, callback in shortcuts.items():
            shortcut = QShortcut(QKeySequence(key_sequence), self.main_window)
            shortcut.activated.connect(callback)
            self.shortcuts[key_sequence] = shortcut
    
    def setup_focus_tracking(self):
        """Setup focus tracking for keyboard navigation"""
        app = QApplication.instance()
        if app:
            app.focusChanged.connect(self.on_focus_changed)
    
    def setup_screen_reader_support(self):
        """Setup screen reader support"""
        # Add accessible names and descriptions to widgets
        self.add_accessible_properties()
        
        # Setup live regions for dynamic content
        self.setup_live_regions()
    
    def setup_high_contrast_mode(self):
        """Setup high contrast mode"""
        # This will be implemented with theme manager integration
        pass
    
    def add_accessible_properties(self):
        """Add accessible names and descriptions to widgets"""
        # Find all widgets and add accessibility properties
        widgets = self.main_window.findChildren(QWidget)
        
        for widget in widgets:
            self.add_widget_accessibility(widget)
    
    def add_widget_accessibility(self, widget: QWidget):
        """Add accessibility properties to a specific widget"""
        widget_type = type(widget).__name__
        object_name = widget.objectName()
        
        # Set accessible names based on widget type and context
        if isinstance(widget, QPushButton):
            if not widget.accessibleName():
                text = widget.text() or object_name
                widget.setAccessibleName(f"Bouton {text}")
                widget.setAccessibleDescription(f"Cliquez pour {text.lower()}")
        
        elif isinstance(widget, QLabel):
            if not widget.accessibleName():
                text = widget.text() or object_name
                widget.setAccessibleName(f"Étiquette {text}")
        
        elif isinstance(widget, QLineEdit):
            if not widget.accessibleName():
                placeholder = widget.placeholderText() or object_name
                widget.setAccessibleName(f"Champ de saisie {placeholder}")
                widget.setAccessibleDescription(f"Entrez {placeholder.lower()}")
        
        elif isinstance(widget, QComboBox):
            if not widget.accessibleName():
                widget.setAccessibleName(f"Liste déroulante {object_name}")
                widget.setAccessibleDescription("Utilisez les flèches pour naviguer")
        
        elif isinstance(widget, QTableWidget):
            if not widget.accessibleName():
                widget.setAccessibleName(f"Tableau {object_name}")
                widget.setAccessibleDescription("Utilisez les flèches pour naviguer dans le tableau")
        
        elif isinstance(widget, QCheckBox):
            if not widget.accessibleName():
                text = widget.text() or object_name
                widget.setAccessibleName(f"Case à cocher {text}")
        
        elif isinstance(widget, QRadioButton):
            if not widget.accessibleName():
                text = widget.text() or object_name
                widget.setAccessibleName(f"Bouton radio {text}")
    
    def setup_live_regions(self):
        """Setup live regions for dynamic content updates"""
        # Create live region for status updates
        if hasattr(self.main_window, 'statusBar'):
            status_bar = self.main_window.statusBar()
            status_bar.setAccessibleName("Barre d'état")
            status_bar.setAccessibleDescription("Informations sur l'état de l'application")
    
    def navigate_to_page(self, page_index: int):
        """Navigate to specific page using keyboard"""
        if hasattr(self.main_window, 'switch_page'):
            self.main_window.switch_page(page_index)
            self.announce_page_change(page_index)
    
    def announce_page_change(self, page_index: int):
        """Announce page change for screen readers"""
        page_names = [
            "Tableau de bord",
            "Administration", 
            "Droits de fourrière",
            "Locaux commerciaux",
            "Affermage des souks",
            "Débits de boissons",
            "Droit de stationnement",
            "Occupation temporaire",
            "Terrains non bâtis"
        ]
        
        if 0 <= page_index < len(page_names):
            page_name = page_names[page_index]
            self.announce(f"Navigation vers {page_name}")
    
    def announce(self, message: str):
        """Announce message for screen readers"""
        # This would integrate with screen reader APIs
        # For now, we'll log the message
        self.logger.info(f"Screen reader announcement: {message}")
        
        # Update status bar for visual feedback
        if hasattr(self.main_window, 'statusBar'):
            self.main_window.statusBar().showMessage(message, 3000)
    
    def on_focus_changed(self, old_widget: QWidget, new_widget: QWidget):
        """Handle focus change events"""
        if new_widget:
            # Add to focus history
            self.focus_history.append(new_widget)
            self.current_focus_index = len(self.focus_history) - 1
            
            # Announce focus change if screen reader mode is enabled
            if self.screen_reader_mode:
                self.announce_focus_change(new_widget)
            
            # Emit signal
            self.focus_changed.emit(new_widget)
    
    def announce_focus_change(self, widget: QWidget):
        """Announce focus change for screen readers"""
        accessible_name = widget.accessibleName()
        accessible_description = widget.accessibleDescription()
        
        if accessible_name:
            message = accessible_name
            if accessible_description:
                message += f". {accessible_description}"
            self.announce(message)
    
    def toggle_high_contrast(self):
        """Toggle high contrast mode"""
        self.high_contrast_mode = not self.high_contrast_mode
        
        if hasattr(self.main_window, 'theme_manager'):
            self.main_window.theme_manager.toggle_high_contrast()
        
        self.announce(f"Mode contraste élevé {'activé' if self.high_contrast_mode else 'désactivé'}")
        self.accessibility_mode_changed.emit(self.high_contrast_mode)
    
    def toggle_large_text(self):
        """Toggle large text mode"""
        self.large_text_mode = not self.large_text_mode
        
        if hasattr(self.main_window, 'theme_manager'):
            size_name = "large" if self.large_text_mode else "normal"
            self.main_window.theme_manager.set_font_size(size_name)
        
        self.announce(f"Mode texte large {'activé' if self.large_text_mode else 'désactivé'}")
    
    def toggle_keyboard_navigation(self):
        """Toggle enhanced keyboard navigation mode"""
        self.keyboard_navigation_mode = not self.keyboard_navigation_mode
        
        # Enable/disable additional keyboard navigation features
        if self.keyboard_navigation_mode:
            self.enable_enhanced_keyboard_navigation()
        else:
            self.disable_enhanced_keyboard_navigation()
        
        self.announce(f"Navigation clavier améliorée {'activée' if self.keyboard_navigation_mode else 'désactivée'}")
    
    def enable_enhanced_keyboard_navigation(self):
        """Enable enhanced keyboard navigation features"""
        # Add additional keyboard shortcuts and navigation aids
        pass
    
    def disable_enhanced_keyboard_navigation(self):
        """Disable enhanced keyboard navigation features"""
        # Remove additional keyboard shortcuts and navigation aids
        pass
    
    def show_help(self):
        """Show accessibility help"""
        help_text = """
        Raccourcis d'accessibilité:
        
        Navigation:
        - Alt****: Naviguer vers les différentes pages
        - Tab/Shift+Tab: Navigation entre les éléments
        - Échap: Fermer les dialogues
        
        Accessibilité:
        - Ctrl+Shift+H: Basculer le mode contraste élevé
        - Ctrl+Shift+L: Basculer le mode texte large
        - Ctrl+Shift+K: Basculer la navigation clavier améliorée
        - F1: Afficher cette aide
        
        Navigation dans les tableaux:
        - Flèches: Naviguer entre les cellules
        - Entrée: Activer l'élément sélectionné
        - Espace: Cocher/décocher les cases
        """
        
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self.main_window, "Aide Accessibilité", help_text)
    
    def handle_escape(self):
        """Handle escape key press"""
        # Close any open dialogs or return to previous state
        focused_widget = QApplication.focusWidget()
        if focused_widget:
            # Try to close parent dialog if it exists
            parent = focused_widget.parent()
            while parent:
                if hasattr(parent, 'close') and hasattr(parent, 'exec_'):
                    parent.close()
                    return
                parent = parent.parent()
    
    def handle_tab_navigation(self):
        """Handle tab navigation"""
        # Enhanced tab navigation logic
        pass
    
    def handle_shift_tab_navigation(self):
        """Handle shift+tab navigation"""
        # Enhanced shift+tab navigation logic
        pass
    
    def get_accessibility_status(self) -> Dict[str, bool]:
        """Get current accessibility settings status"""
        return {
            'high_contrast_mode': self.high_contrast_mode,
            'large_text_mode': self.large_text_mode,
            'keyboard_navigation_mode': self.keyboard_navigation_mode,
            'screen_reader_mode': self.screen_reader_mode
        }


def setup_accessibility(main_window) -> AccessibilityManager:
    """Setup accessibility features for the main window"""
    return AccessibilityManager(main_window)
