/* Modern Material Design Theme for Fiscal Resources Management Application */

/* Global Styles */
QWidget {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    color: #212121;
    background-color: #fafafa;
}

/* Main Window */
QMainWindow {
    background-color: #fafafa;
}

/* Typography */
QLabel#pageTitle {
    color: #1976d2;
    font-size: 28px;
    font-weight: 500;
    margin: 16px 0;
    letter-spacing: 0.25px;
}

QLabel#sectionTitle {
    color: #212121;
    font-size: 20px;
    font-weight: 500;
    margin: 12px 0 8px 0;
}

QLabel#subtitle {
    color: #757575;
    font-size: 16px;
    font-weight: 400;
    margin: 8px 0;
}

/* Menu Bar */
QMenuBar {
    background-color: #1976d2;
    color: white;
    padding: 4px;
    border: none;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #1565c0;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
    margin: 2px;
}

QMenu::item:selected {
    background-color: #1976d2;
    color: white;
}

/* Status Bar */
QStatusBar {
    background-color: #f5f5f5;
    color: #757575;
    padding: 8px;
    border-top: 1px solid #e0e0e0;
    font-size: 12px;
}

/* Modern Material Design Buttons */
QPushButton {
    background-color: #1976d2;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-height: 36px;
}

QPushButton:hover {
    background-color: #1565c0;
}

QPushButton:pressed {
    background-color: #0d47a1;
}

QPushButton:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
}

/* Secondary Button Style */
QPushButton#secondaryButton {
    background-color: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

QPushButton#secondaryButton:hover {
    background-color: rgba(25, 118, 210, 0.04);
}

QPushButton#secondaryButton:pressed {
    background-color: rgba(25, 118, 210, 0.12);
}

/* Danger Button Style */
QPushButton#dangerButton {
    background-color: #d32f2f;
}

QPushButton#dangerButton:hover {
    background-color: #c62828;
}

QPushButton#dangerButton:pressed {
    background-color: #b71c1c;
}

/* Success Button Style */
QPushButton#successButton {
    background-color: #388e3c;
}

QPushButton#successButton:hover {
    background-color: #2e7d32;
}

QPushButton#successButton:pressed {
    background-color: #1b5e20;
}

/* Modern Tab Widget */
QTabWidget::pane {
    border: 1px solid #e0e0e0;
    background-color: #ffffff;
    border-radius: 8px;
    margin-top: 4px;
}

QTabBar::tab {
    background-color: transparent;
    color: #757575;
    padding: 12px 24px;
    border: none;
    border-bottom: 3px solid transparent;
    margin-right: 4px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: transparent;
    border-bottom: 3px solid #1976d2;
    color: #1976d2;
    font-weight: 500;
}

QTabBar::tab:hover:!selected {
    background-color: rgba(25, 118, 210, 0.04);
    color: #1976d2;
}

/* Modern Group Box */
QGroupBox {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    margin-top: 24px;
    font-weight: 500;
    background-color: #ffffff;
    padding-top: 16px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 16px;
    padding: 0 8px;
    color: #1976d2;
    font-size: 16px;
    font-weight: 500;
}

/* Modern Input Fields */
QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    background-color: #ffffff;
    color: #212121;
    font-size: 14px;
    selection-background-color: #1976d2;
    selection-color: white;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus,
QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border: 2px solid #1976d2;
    outline: none;
    background-color: #ffffff;
}

QLineEdit:hover, QTextEdit:hover, QComboBox:hover,
QSpinBox:hover, QDoubleSpinBox:hover, QDateEdit:hover {
    border: 2px solid #757575;
}

QLineEdit:disabled, QTextEdit:disabled, QComboBox:disabled,
QSpinBox:disabled, QDoubleSpinBox:disabled, QDateEdit:disabled {
    background-color: #f5f5f5;
    color: #9e9e9e;
    border: 2px solid #e0e0e0;
}

/* ComboBox specific styling */
QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    width: 16px;
    height: 16px;
}

QComboBox QAbstractItemView {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #ffffff;
    selection-background-color: #1976d2;
    selection-color: white;
    padding: 4px;
}

/* Modern Tables */
QTableWidget {
    gridline-color: #e0e0e0;
    background-color: #ffffff;
    alternate-background-color: #fafafa;
    selection-background-color: rgba(25, 118, 210, 0.12);
    selection-color: #1976d2;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
}

QTableWidget::item {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    border-right: none;
}

QTableWidget::item:selected {
    background-color: rgba(25, 118, 210, 0.12);
    color: #1976d2;
}

QTableWidget::item:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Modern Table Headers */
QHeaderView::section {
    background-color: #f5f5f5;
    color: #212121;
    padding: 16px;
    border: none;
    border-bottom: 2px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

QHeaderView::section:hover {
    background-color: #eeeeee;
}

QHeaderView::section:pressed {
    background-color: #e0e0e0;
}

/* Modern Scroll Bars */
QScrollBar:vertical {
    background-color: #f5f5f5;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::handle:vertical:pressed {
    background-color: #757575;
}

QScrollBar:horizontal {
    background-color: #f5f5f5;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #757575;
}

QScrollBar::add-line, QScrollBar::sub-line {
    border: none;
    background: none;
}

/* Progress Bar */
QProgressBar {
    border: none;
    border-radius: 4px;
    background-color: #e0e0e0;
    text-align: center;
    font-weight: 500;
    color: #212121;
}

QProgressBar::chunk {
    background-color: #1976d2;
    border-radius: 4px;
}

/* Check Box */
QCheckBox {
    spacing: 8px;
    color: #212121;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #757575;
    border-radius: 3px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #1976d2;
    border: 2px solid #1976d2;
}

QCheckBox::indicator:hover {
    border: 2px solid #1976d2;
}

/* Radio Button */
QRadioButton {
    spacing: 8px;
    color: #212121;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #757575;
    border-radius: 9px;
    background-color: #ffffff;
}

QRadioButton::indicator:checked {
    background-color: #1976d2;
    border: 2px solid #1976d2;
}

QRadioButton::indicator:hover {
    border: 2px solid #1976d2;
}

/* Splitter */
QSplitter::handle {
    background-color: #e0e0e0;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

QSplitter::handle:hover {
    background-color: #1976d2;
}

/* Tool Tip */
QToolTip {
    background-color: #424242;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
}
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #cccccc;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox::down-arrow {
    image: url(app/ui/resources/icons/down-arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #cccccc;
    selection-background-color: #3498db;
    selection-color: white;
    background-color: white;
    outline: 0;
}

/* Spin Box */
QSpinBox, QDoubleSpinBox {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border: 1px solid #3498db;
}

/* Check Box */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    image: url(app/ui/resources/icons/checkbox-unchecked.png);
}

QCheckBox::indicator:checked {
    image: url(app/ui/resources/icons/checkbox-checked.png);
}

/* Radio Button */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
}

QRadioButton::indicator:unchecked {
    image: url(app/ui/resources/icons/radio-unchecked.png);
}

QRadioButton::indicator:checked {
    image: url(app/ui/resources/icons/radio-checked.png);
}

/* Date Edit */
QDateEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QDateEdit:focus {
    border: 1px solid #3498db;
}

QDateEdit::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #cccccc;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QDateEdit::down-arrow {
    image: url(app/ui/resources/icons/calendar.png);
    width: 12px;
    height: 12px;
}

/* Table Widget */
QTableWidget {
    border: 1px solid #cccccc;
    background-color: white;
    gridline-color: #eeeeee;
    selection-background-color: #d6eaf8;
    selection-color: #333333;
    alternate-background-color: #f9f9f9;
}

QTableWidget::item {
    padding: 6px;
}

QTableWidget::item:selected {
    background-color: #d6eaf8;
    color: #333333;
}

QHeaderView::section {
    background-color: #3498db;
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
}

QHeaderView::section:horizontal {
    border-right: 1px solid #2980b9;
}

QHeaderView::section:vertical {
    border-bottom: 1px solid #2980b9;
}

/* Scroll Bar */
QScrollBar:vertical {
    border: none;
    background-color: #f0f0f0;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #f0f0f0;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Dialog */
QDialog {
    background-color: #f5f5f5;
}

QDialogButtonBox {
    button-layout: 3;
}

/* Labels */
QLabel {
    color: #333333;
}

QLabel#pageTitle {
    color: #2c3e50;
    font-size: 18pt;
    font-weight: bold;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #cccccc;
    border-radius: 4px;
    text-align: center;
    background-color: white;
}

QProgressBar::chunk {
    background-color: #3498db;
    width: 10px;
    margin: 0.5px;
}

/* Tool Tip */
QToolTip {
    border: 1px solid #cccccc;
    background-color: #ffffcc;
    color: #333333;
    padding: 4px;
    border-radius: 2px;
    opacity: 220;
}

/* Message Box */
QMessageBox {
    background-color: #f5f5f5;
}

QMessageBox QPushButton {
    min-width: 80px;
}

/* Custom Styles for Specific Widgets */
#sidebarWidget {
    background-color: #2c3e50;
    color: white;
}

#sidebarButton {
    background-color: transparent;
    color: white;
    text-align: left;
    padding: 12px;
    border: none;
    border-radius: 0;
    font-weight: normal;
}

#sidebarButton:hover {
    background-color: #34495e;
}

#sidebarButton:checked {
    background-color: #3498db;
    font-weight: bold;
}

#headerWidget {
    background-color: #3498db;
    color: white;
}

#footerWidget {
    background-color: #2c3e50;
    color: white;
    font-size: 9pt;
}

#dashboardTile {
    background-color: white;
    border-radius: 8px;
    padding: 16px;
}

#dashboardTileTitle {
    color: #3498db;
    font-size: 14pt;
    font-weight: bold;
}

#dashboardTileValue {
    color: #2c3e50;
    font-size: 24pt;
    font-weight: bold;
}