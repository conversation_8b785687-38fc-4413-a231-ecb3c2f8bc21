#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Base widget classes for the Fiscal Resources Management Application
Provides reusable components and common patterns
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTabWidget,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QProgressBar,
    QSplitter, QGroupBox, QFormLayout, QCheckBox, QTextEdit
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap
from typing import Dict, List, Any, Optional, Callable
import logging


class BaseModuleWidget(QWidget):
    """Base class for all module widgets with common functionality"""
    
    # Signals
    data_changed = pyqtSignal()
    loading_started = pyqtSignal()
    loading_finished = pyqtSignal()
    
    def __init__(self, db_manager, auth, module_name: str):
        """Initialize base module widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        self.module_name = module_name
        self.logger = logging.getLogger(f"{__name__}.{module_name}")
        
        # Data caching
        self._cache = {}
        self._cache_timeout = 300  # 5 minutes
        self._last_refresh = {}
        
        # UI components
        self.loading_bar = None
        self.status_label = None
        
        self.setup_ui()
        self.setup_signals()
    
    def setup_ui(self):
        """Setup the user interface - to be overridden by subclasses"""
        # Create main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(20)
        
        # Create header
        self.create_header()
        
        # Create content area
        self.create_content()
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create module header with title and actions"""
        header_layout = QHBoxLayout()
        
        # Title
        title_label = QLabel(self.get_module_title())
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Action buttons
        self.create_action_buttons(header_layout)
        
        self.main_layout.addLayout(header_layout)
    
    def create_action_buttons(self, layout: QHBoxLayout):
        """Create action buttons - to be overridden by subclasses"""
        # Refresh button
        refresh_btn = QPushButton("Actualiser")
        refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_btn)
    
    def create_content(self):
        """Create main content area - to be overridden by subclasses"""
        # Default tab widget
        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget)
    
    def create_status_bar(self):
        """Create status bar with loading indicator"""
        status_layout = QHBoxLayout()
        
        # Status label
        self.status_label = QLabel("Prêt")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # Loading bar
        self.loading_bar = QProgressBar()
        self.loading_bar.setVisible(False)
        self.loading_bar.setMaximumWidth(200)
        status_layout.addWidget(self.loading_bar)
        
        self.main_layout.addLayout(status_layout)
    
    def setup_signals(self):
        """Setup signal connections"""
        self.loading_started.connect(self.show_loading)
        self.loading_finished.connect(self.hide_loading)
    
    def get_module_title(self) -> str:
        """Get module title - to be overridden by subclasses"""
        return self.module_name
    
    def show_loading(self, message: str = "Chargement..."):
        """Show loading indicator"""
        self.loading_bar.setVisible(True)
        self.loading_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText(message)
    
    def hide_loading(self, message: str = "Prêt"):
        """Hide loading indicator"""
        self.loading_bar.setVisible(False)
        self.status_label.setText(message)
    
    def refresh_data(self):
        """Refresh all data - to be overridden by subclasses"""
        self.loading_started.emit()
        
        # Clear cache
        self._cache.clear()
        self._last_refresh.clear()
        
        # Emit signal for subclasses to handle
        self.data_changed.emit()
        
        self.loading_finished.emit("Données actualisées")
    
    def get_cached_data(self, key: str, fetch_func: Callable) -> Any:
        """Get data from cache or fetch if expired"""
        import time
        
        current_time = time.time()
        
        # Check if data is cached and not expired
        if (key in self._cache and 
            key in self._last_refresh and 
            current_time - self._last_refresh[key] < self._cache_timeout):
            return self._cache[key]
        
        # Fetch fresh data
        data = fetch_func()
        self._cache[key] = data
        self._last_refresh[key] = current_time
        
        return data
    
    def show_error(self, title: str, message: str):
        """Show error message to user"""
        QMessageBox.critical(self, title, message)
        self.logger.error(f"{title}: {message}")
    
    def show_success(self, title: str, message: str):
        """Show success message to user"""
        QMessageBox.information(self, title, message)
        self.logger.info(f"{title}: {message}")
    
    def show_warning(self, title: str, message: str):
        """Show warning message to user"""
        QMessageBox.warning(self, title, message)
        self.logger.warning(f"{title}: {message}")
    
    def confirm_action(self, title: str, message: str) -> bool:
        """Show confirmation dialog"""
        reply = QMessageBox.question(
            self, title, message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes


class BaseTableWidget(QTableWidget):
    """Enhanced table widget with common functionality"""
    
    # Signals
    row_double_clicked = pyqtSignal(dict)
    selection_changed = pyqtSignal(list)
    
    def __init__(self, columns: List[str], parent=None):
        """Initialize base table widget"""
        super().__init__(parent)
        
        self.columns = columns
        self.data_rows = []
        
        self.setup_table()
        self.setup_signals()
    
    def setup_table(self):
        """Setup table properties"""
        # Set columns
        self.setColumnCount(len(self.columns))
        self.setHorizontalHeaderLabels(self.columns)
        
        # Table properties
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.setSortingEnabled(True)
        
        # Header properties
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # Hide vertical header
        self.verticalHeader().setVisible(False)
    
    def setup_signals(self):
        """Setup signal connections"""
        self.itemDoubleClicked.connect(self._on_double_click)
        self.itemSelectionChanged.connect(self._on_selection_changed)
    
    def load_data(self, data: List[Dict[str, Any]]):
        """Load data into table"""
        self.data_rows = data
        self.setRowCount(len(data))
        
        for row_idx, row_data in enumerate(data):
            for col_idx, column in enumerate(self.columns):
                value = row_data.get(column, "")
                item = QTableWidgetItem(str(value))
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.setItem(row_idx, col_idx, item)
    
    def add_action_column(self, column_name: str, button_text: str, callback: Callable):
        """Add action column with buttons"""
        # Add column
        self.columns.append(column_name)
        self.setColumnCount(len(self.columns))
        self.setHorizontalHeaderLabels(self.columns)
        
        # Add buttons for existing rows
        for row_idx in range(self.rowCount()):
            btn = QPushButton(button_text)
            btn.clicked.connect(lambda checked, r=row_idx: callback(self.data_rows[r]))
            self.setCellWidget(row_idx, len(self.columns) - 1, btn)
    
    def get_selected_data(self) -> Optional[Dict[str, Any]]:
        """Get data for selected row"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.data_rows):
            return self.data_rows[current_row]
        return None
    
    def _on_double_click(self, item):
        """Handle double click on item"""
        row_data = self.get_selected_data()
        if row_data:
            self.row_double_clicked.emit(row_data)
    
    def _on_selection_changed(self):
        """Handle selection change"""
        selected_data = []
        for item in self.selectedItems():
            if item.row() < len(self.data_rows):
                selected_data.append(self.data_rows[item.row()])
        self.selection_changed.emit(selected_data)


class SearchWidget(QWidget):
    """Reusable search widget"""
    
    # Signals
    search_requested = pyqtSignal(dict)
    search_cleared = pyqtSignal()
    
    def __init__(self, search_fields: List[Dict[str, str]], parent=None):
        """
        Initialize search widget
        search_fields: List of dicts with 'name', 'label', 'type' keys
        """
        super().__init__(parent)
        
        self.search_fields = search_fields
        self.field_widgets = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup search UI"""
        layout = QVBoxLayout(self)
        
        # Search group
        search_group = QGroupBox("Recherche")
        search_layout = QFormLayout(search_group)
        
        # Create search fields
        for field in self.search_fields:
            widget = self._create_field_widget(field)
            self.field_widgets[field['name']] = widget
            search_layout.addRow(field['label'], widget)
        
        layout.addWidget(search_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        search_btn = QPushButton("Rechercher")
        search_btn.clicked.connect(self.perform_search)
        button_layout.addWidget(search_btn)
        
        clear_btn = QPushButton("Effacer")
        clear_btn.clicked.connect(self.clear_search)
        button_layout.addWidget(clear_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
    
    def _create_field_widget(self, field: Dict[str, str]) -> QWidget:
        """Create widget for search field"""
        field_type = field.get('type', 'text')
        
        if field_type == 'text':
            widget = QLineEdit()
            widget.setPlaceholderText(f"Rechercher par {field['label'].lower()}")
        elif field_type == 'combo':
            widget = QComboBox()
            widget.addItem("Tous", "")
            # Add options if provided
            for option in field.get('options', []):
                widget.addItem(option['label'], option['value'])
        elif field_type == 'date':
            widget = QDateEdit()
            widget.setCalendarPopup(True)
            widget.setDate(QDate.currentDate())
        else:
            widget = QLineEdit()
        
        return widget
    
    def perform_search(self):
        """Perform search with current field values"""
        search_params = {}
        
        for field_name, widget in self.field_widgets.items():
            if isinstance(widget, QLineEdit):
                value = widget.text().strip()
            elif isinstance(widget, QComboBox):
                value = widget.currentData()
            elif isinstance(widget, QDateEdit):
                value = widget.date().toString(Qt.ISODate)
            else:
                value = ""
            
            if value:
                search_params[field_name] = value
        
        self.search_requested.emit(search_params)
    
    def clear_search(self):
        """Clear all search fields"""
        for widget in self.field_widgets.values():
            if isinstance(widget, QLineEdit):
                widget.clear()
            elif isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)
            elif isinstance(widget, QDateEdit):
                widget.setDate(QDate.currentDate())
        
        self.search_cleared.emit()
    
    def get_search_params(self) -> Dict[str, Any]:
        """Get current search parameters"""
        search_params = {}
        
        for field_name, widget in self.field_widgets.items():
            if isinstance(widget, QLineEdit):
                value = widget.text().strip()
            elif isinstance(widget, QComboBox):
                value = widget.currentData()
            elif isinstance(widget, QDateEdit):
                value = widget.date().toString(Qt.ISODate)
            else:
                value = ""
            
            if value:
                search_params[field_name] = value
        
        return search_params
