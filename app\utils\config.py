#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration manager for the application
Handles loading and saving application settings
"""

import os
import json
import sqlite3

class Config:
    """Configuration manager class"""
    
    def __init__(self, config_file="config.json"):
        """Initialize configuration manager"""
        self.config_file = config_file
        self.db_path = "database/fiscal_resources.db"
        self.config = self._load_config()
    
    def _load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading config file: {e}")
                return self._get_default_config()
        else:
            return self._get_default_config()
    
    def _get_default_config(self):
        """Return default configuration"""
        return {
            "language": "fr",
            "theme": "light",
            "window_size": {
                "width": 1200,
                "height": 800
            },
            "database": {
                "path": self.db_path
            }
        }
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            print(f"Error saving config file: {e}")
            return False
    
    def get_setting(self, key, default=None):
        """Get a setting value"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_setting(self, key, value):
        """Set a setting value"""
        keys = key.split('.')
        config = self.config
        
        for i, k in enumerate(keys[:-1]):
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()
    
    def get_db_setting(self, category, key, default=None):
        """Get a setting from the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT value FROM configuration WHERE category = ? AND key = ?",
                (category, key)
            )
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0]
            else:
                return default
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return default
    
    def set_db_setting(self, category, key, value):
        """Set a setting in the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                """
                INSERT INTO configuration (category, key, value) 
                VALUES (?, ?, ?) 
                ON CONFLICT(category, key) 
                DO UPDATE SET value = ?
                """,
                (category, key, value, value)
            )
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return False