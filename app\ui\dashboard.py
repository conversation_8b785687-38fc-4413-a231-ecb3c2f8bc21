#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dashboard widget for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QScrollArea, QGridLayout, QSizePolicy
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

class StatCard(QFrame):
    """Statistic card widget for dashboard"""
    
    def __init__(self, title, value, icon=None, color="#3498db"):
        """Initialize stat card"""
        super().__init__()
        
        self.setObjectName("statCard")
        self.setMinimumHeight(120)
        self.setStyleSheet(f"""
            #statCard {{
                background-color: {color};
                border-radius: 8px;
                color: white;
            }}
            
            #statTitle {{
                font-size: 14px;
                font-weight: bold;
            }}
            
            #statValue {{
                font-size: 24px;
                font-weight: bold;
            }}
        """)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Create title
        title_label = QLabel(title)
        title_label.setObjectName("statTitle")
        layout.addWidget(title_label)
        
        # Create value
        value_label = QLabel(str(value))
        value_label.setObjectName("statValue")
        layout.addWidget(value_label)
        
        # Add icon if provided
        if icon:
            pass  # TODO: Add icon support

class DashboardWidget(QWidget):
    """Dashboard widget for the main application"""
    
    def __init__(self, db_manager, auth):
        """Initialize dashboard widget"""
        super().__init__()
        
        self.db_manager = db_manager
        self.auth = auth
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Tableau de Bord")
        title_label.setObjectName("pageTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)
        
        # Create scroll area for content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)
        
        # Add statistics cards
        stats_layout = QGridLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(20)
        
        # Add stat cards
        stats_layout.addWidget(StatCard("Véhicules en Fourrière", self.get_impound_count(), color="#3498db"), 0, 0)
        stats_layout.addWidget(StatCard("Boutiques Louées", self.get_shops_count(), color="#2ecc71"), 0, 1)
        stats_layout.addWidget(StatCard("Débits de Boissons", self.get_beverages_count(), color="#e74c3c"), 0, 2)
        stats_layout.addWidget(StatCard("Terrains Non Bâtis", self.get_lands_count(), color="#f39c12"), 1, 0)
        stats_layout.addWidget(StatCard("Paiements en Attente", self.get_pending_payments_count(), color="#9b59b6"), 1, 1)
        stats_layout.addWidget(StatCard("Revenus du Mois", f"{self.get_monthly_revenue():,.2f} DH", color="#1abc9c"), 1, 2)
        
        scroll_layout.addLayout(stats_layout)
        
        # Add recent activity section
        activity_frame = QFrame()
        activity_frame.setObjectName("activityFrame")
        activity_frame.setStyleSheet("""
            #activityFrame {
                background-color: white;
                border-radius: 8px;
            }
            
            #activityTitle {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
        activity_layout = QVBoxLayout(activity_frame)
        
        activity_title = QLabel("Activité Récente")
        activity_title.setObjectName("activityTitle")
        activity_layout.addWidget(activity_title)
        
        # TODO: Add actual recent activity data
        activity_layout.addWidget(QLabel("Aucune activité récente à afficher."))
        
        scroll_layout.addWidget(activity_frame)
        
        # Add upcoming payments section
        upcoming_frame = QFrame()
        upcoming_frame.setObjectName("upcomingFrame")
        upcoming_frame.setStyleSheet("""
            #upcomingFrame {
                background-color: white;
                border-radius: 8px;
            }
            
            #upcomingTitle {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
        upcoming_layout = QVBoxLayout(upcoming_frame)
        
        upcoming_title = QLabel("Paiements à Venir")
        upcoming_title.setObjectName("upcomingTitle")
        upcoming_layout.addWidget(upcoming_title)
        
        # TODO: Add actual upcoming payments data
        upcoming_layout.addWidget(QLabel("Aucun paiement à venir à afficher."))
        
        scroll_layout.addWidget(upcoming_frame)
        
        # Add stretch to push everything to the top
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
    
    def get_impound_count(self):
        """Get count of vehicles in impound"""
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM impound_vehicles WHERE status = 'current'"
        )
        return result["count"] if result else 0
    
    def get_shops_count(self):
        """Get count of rented shops"""
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM commercial_shops WHERE owner_name IS NOT NULL"
        )
        return result["count"] if result else 0
    
    def get_beverages_count(self):
        """Get count of beverage establishments"""
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM beverage_establishments WHERE status = 'active'"
        )
        return result["count"] if result else 0
    
    def get_lands_count(self):
        """Get count of undeveloped lands"""
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM undeveloped_lands WHERE status = 'active'"
        )
        return result["count"] if result else 0
    
    def get_pending_payments_count(self):
        """Get count of pending payments"""
        count = 0
        
        # Impound payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM impound_vehicles WHERE status = 'pending_payment'"
        )
        count += result["count"] if result else 0
        
        # Shop payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM shop_payments WHERE status = 'initiated'"
        )
        count += result["count"] if result else 0
        
        # Souk payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM souk_payments WHERE status = 'initiated'"
        )
        count += result["count"] if result else 0
        
        # Beverage payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM beverage_payments WHERE status = 'initiated'"
        )
        count += result["count"] if result else 0
        
        # Parking payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM parking_payments WHERE status = 'initiated'"
        )
        count += result["count"] if result else 0
        
        # Occupation payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM occupation_payments WHERE status = 'initiated'"
        )
        count += result["count"] if result else 0
        
        # Land payments
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM land_payments WHERE status = 'initiated'"
        )
        count += result["count"] if result else 0
        
        return count
    
    def get_monthly_revenue(self):
        """Get total revenue for the current month"""
        # This is a placeholder - in a real implementation, we would calculate
        # the actual revenue from all payment sources for the current month
        return 0.0