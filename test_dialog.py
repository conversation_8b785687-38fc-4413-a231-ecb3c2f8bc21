#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for dialog boxes
"""

import sys
from PyQt5.QtWidgets import QApplication
from app.ui.dialogs import VehicleDialog
from app.database.db_manager import DatabaseManager

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Create a database manager
    db_manager = DatabaseManager()
    
    # Create and show the dialog
    dialog = VehicleDialog(db_manager)
    result = dialog.exec_()
    
    if result == VehicleDialog.Accepted:
        print("Dialog accepted!")
        data = dialog.get_vehicle_data()
        print(f"Vehicle data: {data}")
    else:
        print("Dialog rejected!")
    
    sys.exit(0)