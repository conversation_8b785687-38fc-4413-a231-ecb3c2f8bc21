/* Modern Theme for Fiscal Resources Management Application */

/* Global Styles */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
    color: #333333;
    background-color: #f5f5f5;
}

/* Main Window */
QMainWindow {
    background-color: #f5f5f5;
}

/* Menu Bar */
QMenuBar {
    background-color: #2c3e50;
    color: white;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #34495e;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    padding: 5px;
}

QMenu::item {
    padding: 6px 25px 6px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: white;
}

/* Status Bar */
QStatusBar {
    background-color: #2c3e50;
    color: white;
    padding: 3px;
    font-size: 9pt;
}

/* Buttons */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1a5276;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #999999;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #cccccc;
    background-color: white;
    border-radius: 4px;
}

QTabBar::tab {
    background-color: #ecf0f1;
    color: #555555;
    padding: 8px 16px;
    border: 1px solid #cccccc;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 2px solid #3498db;
    color: #3498db;
    font-weight: bold;
}

QTabBar::tab:hover:!selected {
    background-color: #d6eaf8;
}

/* Group Box */
QGroupBox {
    border: 1px solid #cccccc;
    border-radius: 4px;
    margin-top: 20px;
    font-weight: bold;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0 5px;
    color: #3498db;
}

/* Line Edit */
QLineEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
    selection-background-color: #3498db;
}

QLineEdit:focus {
    border: 1px solid #3498db;
}

QLineEdit:disabled {
    background-color: #f0f0f0;
    color: #999999;
}

/* Combo Box */
QComboBox {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
    selection-background-color: #3498db;
}

QComboBox:focus {
    border: 1px solid #3498db;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #cccccc;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox::down-arrow {
    image: url(app/ui/resources/icons/down-arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #cccccc;
    selection-background-color: #3498db;
    selection-color: white;
    background-color: white;
    outline: 0;
}

/* Spin Box */
QSpinBox, QDoubleSpinBox {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border: 1px solid #3498db;
}

/* Check Box */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    image: url(app/ui/resources/icons/checkbox-unchecked.png);
}

QCheckBox::indicator:checked {
    image: url(app/ui/resources/icons/checkbox-checked.png);
}

/* Radio Button */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
}

QRadioButton::indicator:unchecked {
    image: url(app/ui/resources/icons/radio-unchecked.png);
}

QRadioButton::indicator:checked {
    image: url(app/ui/resources/icons/radio-checked.png);
}

/* Date Edit */
QDateEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QDateEdit:focus {
    border: 1px solid #3498db;
}

QDateEdit::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #cccccc;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QDateEdit::down-arrow {
    image: url(app/ui/resources/icons/calendar.png);
    width: 12px;
    height: 12px;
}

/* Table Widget */
QTableWidget {
    border: 1px solid #cccccc;
    background-color: white;
    gridline-color: #eeeeee;
    selection-background-color: #d6eaf8;
    selection-color: #333333;
    alternate-background-color: #f9f9f9;
}

QTableWidget::item {
    padding: 6px;
}

QTableWidget::item:selected {
    background-color: #d6eaf8;
    color: #333333;
}

QHeaderView::section {
    background-color: #3498db;
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
}

QHeaderView::section:horizontal {
    border-right: 1px solid #2980b9;
}

QHeaderView::section:vertical {
    border-bottom: 1px solid #2980b9;
}

/* Scroll Bar */
QScrollBar:vertical {
    border: none;
    background-color: #f0f0f0;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #f0f0f0;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Dialog */
QDialog {
    background-color: #f5f5f5;
}

QDialogButtonBox {
    button-layout: 3;
}

/* Labels */
QLabel {
    color: #333333;
}

QLabel#pageTitle {
    color: #2c3e50;
    font-size: 18pt;
    font-weight: bold;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #cccccc;
    border-radius: 4px;
    text-align: center;
    background-color: white;
}

QProgressBar::chunk {
    background-color: #3498db;
    width: 10px;
    margin: 0.5px;
}

/* Tool Tip */
QToolTip {
    border: 1px solid #cccccc;
    background-color: #ffffcc;
    color: #333333;
    padding: 4px;
    border-radius: 2px;
    opacity: 220;
}

/* Message Box */
QMessageBox {
    background-color: #f5f5f5;
}

QMessageBox QPushButton {
    min-width: 80px;
}

/* Custom Styles for Specific Widgets */
#sidebarWidget {
    background-color: #2c3e50;
    color: white;
}

#sidebarButton {
    background-color: transparent;
    color: white;
    text-align: left;
    padding: 12px;
    border: none;
    border-radius: 0;
    font-weight: normal;
}

#sidebarButton:hover {
    background-color: #34495e;
}

#sidebarButton:checked {
    background-color: #3498db;
    font-weight: bold;
}

#headerWidget {
    background-color: #3498db;
    color: white;
}

#footerWidget {
    background-color: #2c3e50;
    color: white;
    font-size: 9pt;
}

#dashboardTile {
    background-color: white;
    border-radius: 8px;
    padding: 16px;
}

#dashboardTileTitle {
    color: #3498db;
    font-size: 14pt;
    font-weight: bold;
}

#dashboardTileValue {
    color: #2c3e50;
    font-size: 24pt;
    font-weight: bold;
}