#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Login dialog for the Fiscal Resources Management Application
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QFrame, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

class LoginDialog(QDialog):
    """Login dialog for user authentication"""
    
    def __init__(self, auth):
        """Initialize login dialog"""
        super().__init__()
        
        self.auth = auth
        self.setWindowTitle("Connexion")
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create header
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setMinimumHeight(60)
        header_frame.setMaximumHeight(60)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 0, 20, 0)
        
        header_label = QLabel("Gestion des Ressources Fiscales")
        header_label.setObjectName("headerLabel")
        header_font = QFont()
        header_font.setPointSize(12)
        header_font.setBold(True)
        header_label.setFont(header_font)
        
        header_layout.addWidget(header_label)
        main_layout.addWidget(header_frame)
        
        # Create content
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(40, 30, 40, 30)
        content_layout.setSpacing(20)
        
        # Title
        title_label = QLabel("Connexion")
        title_label.setObjectName("titleLabel")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        content_layout.addWidget(title_label)
        
        # Username
        username_label = QLabel("Nom d'utilisateur:")
        username_label.setObjectName("fieldLabel")
        
        self.username_input = QLineEdit()
        self.username_input.setObjectName("fieldInput")
        self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        
        content_layout.addWidget(username_label)
        content_layout.addWidget(self.username_input)
        
        # Password
        password_label = QLabel("Mot de passe:")
        password_label.setObjectName("fieldLabel")
        
        self.password_input = QLineEdit()
        self.password_input.setObjectName("fieldInput")
        self.password_input.setPlaceholderText("Entrez votre mot de passe")
        self.password_input.setEchoMode(QLineEdit.Password)
        
        content_layout.addWidget(password_label)
        content_layout.addWidget(self.password_input)
        
        # Login button
        self.login_btn = QPushButton("Se Connecter")
        self.login_btn.setObjectName("loginButton")
        self.login_btn.setMinimumHeight(40)
        self.login_btn.clicked.connect(self.login)
        
        content_layout.addWidget(self.login_btn)
        
        # Cancel button
        self.cancel_btn = QPushButton("Annuler")
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.clicked.connect(self.reject)
        
        content_layout.addWidget(self.cancel_btn)
        
        main_layout.addWidget(content_frame)
        
        # Apply stylesheet
        self.apply_stylesheet()
        
        # Set focus to username input
        self.username_input.setFocus()
    
    def login(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(
                self,
                "Champs Requis",
                "Veuillez remplir tous les champs."
            )
            return
        
        if self.auth.login(username, password):
            self.accept()
        else:
            QMessageBox.critical(
                self,
                "Échec de Connexion",
                "Nom d'utilisateur ou mot de passe incorrect."
            )
            self.password_input.clear()
            self.password_input.setFocus()
    
    def apply_stylesheet(self):
        """Apply stylesheet to the dialog"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ecf0f1;
            }
            
            #headerFrame {
                background-color: #2c3e50;
                border: none;
            }
            
            #headerLabel {
                color: #ecf0f1;
            }
            
            #contentFrame {
                background-color: #ecf0f1;
            }
            
            #titleLabel {
                color: #2c3e50;
            }
            
            #fieldLabel {
                color: #2c3e50;
                font-size: 14px;
            }
            
            #fieldInput {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                background-color: #ffffff;
            }
            
            #fieldInput:focus {
                border: 1px solid #3498db;
            }
            
            #loginButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            
            #loginButton:hover {
                background-color: #2980b9;
            }
            
            #cancelButton {
                background-color: #e74c3c;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                font-size: 14px;
            }
            
            #cancelButton:hover {
                background-color: #c0392b;
            }
        """)